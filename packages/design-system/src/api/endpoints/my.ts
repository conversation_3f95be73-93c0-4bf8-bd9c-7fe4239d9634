import { EndpointConfig, EndpointKeys } from './type';

export const EndpointsMY: Record<EndpointKeys, EndpointConfig> = {
  [EndpointKeys.getEnv]: {
    path: 'v5/env-my/get-asker-env',
  },
  [EndpointKeys.getAllSettingsWithoutLogin]: {
    path: 'v5/api-asker-my/get-all-settings-without-login',
  },
  [EndpointKeys.getAllSettings]: {
    path: 'v5/api-asker-my/get-all-settings',
  },
  [EndpointKeys.getUser]: {
    path: 'v5/api-asker-my/get-user',
  },
  [EndpointKeys.getUpComingTasks]: {
    path: 'v5/api-asker-my/get-up-coming-tasks',
  },
  [EndpointKeys.getScheduleTasks]: {
    path: 'v5/api-asker-my/get-schedule-tasks',
  },
  [EndpointKeys.getMonthlyTasks]: {
    path: 'v5/api-asker-my/get-subscription-by-userId',
  },
  [EndpointKeys.checkTaskSameTime]: {
    path: 'v5/api-asker-my/check-task-sametime',
  },
  [EndpointKeys.postTaskAirConditioner]: {
    path: 'v5/booking-my/air-conditioner',
  },
  [EndpointKeys.postTaskChildCare]: {
    path: 'v5/booking-my/child-care',
  },
  [EndpointKeys.postTaskElderlyCare]: {
    path: 'v5/booking-my/elderly-care',
  },
  [EndpointKeys.postTaskPatientCare]: {
    path: 'v5/booking-my/patient-care',
  },
  [EndpointKeys.postTaskOfficeCleaning]: {
    path: 'v5/booking-my/office-cleaning',
  },
  [EndpointKeys.checkTaskerConflictTime]: {
    path: 'v5/api-asker-my/check-tasker-conflict-time',
  },
  [EndpointKeys.postTaskSubscriptionCleaning]: {
    path: 'v5/booking-my/subscription',
  },
  [EndpointKeys.postTaskOfficeCleaningSubscription]: {
    path: 'v5/booking-my/subscription-office-cleaning',
  },
  [EndpointKeys.getFinancialAccount]: {
    path: 'v5/api-asker-my/get-financial-account',
  },
  [EndpointKeys.bookTaskForceTasker]: {
    path: 'v5/booking-my/book-task-force-tasker',
  },
  [EndpointKeys.postTaskCleaning]: {
    path: 'v5/booking-my/home-cleaning',
  },
  [EndpointKeys.getOutstandingPayment]: {
    path: 'v5/api-asker-my/get-outstanding-payment',
  },
  [EndpointKeys.getTaskDetail]: {
    path: 'v5/api-asker-my/get-task-detail',
  },
  [EndpointKeys.cancelTask]: {
    path: 'v5/cancel-task-my/cancel',
  },
  [EndpointKeys.addFavoriteService]: {
    path: 'v5/api-asker-my/add-favourite-services',
  },
  [EndpointKeys.postTaskWaterHeater]: {
    path: 'v5/booking-my/water-heater',
  },
  [EndpointKeys.postTaskMassage]: {
    path: 'v5/booking-my/massage',
  },
  // Housekeeping
  [EndpointKeys.createHousekeepingLocation]: {
    path: 'v5/api-asker-my/add-housekeeping-location',
  },
  [EndpointKeys.updateRoomHousekeepingLocation]: {
    path: 'v5/api-asker-my/update-housekeeping-location',
  },
  [EndpointKeys.deleteRoomHousekeepingLocation]: {
    path: 'v5/api-asker-my/delete-housekeeping-location',
  },
  [EndpointKeys.postTaskHousekeeping]: {
    path: 'v5/booking-my/housekeeping-v2',
  },
  [EndpointKeys.postTaskDeepCleaning]: {
    path: 'v5/booking-my/deep-cleaning',
  },

  // Update task
  [EndpointKeys.changePremiumOption]: {
    path: 'v5/update-task-my/update-task-premium',
  },
  [EndpointKeys.updateTaskNote]: {
    path: 'v5/update-task-my/update-task-note',
  },
  [EndpointKeys.getHistoryTasks]: {
    path: 'v5/api-asker-my/get-list-history-tasks',
  },
  [EndpointKeys.getExtraMoneyUpdateDateTime]: {
    path: 'v5/update-task-my/get-extra-money-update-date-time',
  },
  [EndpointKeys.createUpdateDateTimeRequest]: {
    path: 'v5/update-task-my/create-update-date-time-request',
  },
  [EndpointKeys.askerCreateRequestUpdateDetailV4]: {
    path: 'v5/update-task-my/asker-create-request',
  },
  [EndpointKeys.updateTaskHomeCleaning]: {
    path: 'v5/update-task-my/home-cleaning',
  },
  [EndpointKeys.updateHouseKeeping]: {
    path: 'v5/update-task-my/house-keeping',
  },
  [EndpointKeys.updateTaskHomeCooking]: {
    path: 'v5/update-task-my/home-cooking',
  },
  [EndpointKeys.updateTaskAirConditioner]: {
    path: 'v5/update-task-my/air-conditioner',
  },
  [EndpointKeys.updateTaskDeepCleaning]: {
    path: 'v5/update-task-my/deep-cleaning',
  },
  [EndpointKeys.updateTaskChildCare]: {
    path: 'v5/update-task-my/child-care',
  },
  [EndpointKeys.updateTaskDisinfection]: {
    path: 'v5/update-task-my/disinfection',
  },
  [EndpointKeys.updateTaskPatientCare]: {
    path: 'v5/update-task-my/patient-care',
  },
  [EndpointKeys.updateTaskElderlyCare]: {
    path: 'v5/update-task-my/elderly-care',
  },
  [EndpointKeys.updateTaskSofa]: {
    path: 'v5/update-task-my/sofa',
  },
  [EndpointKeys.updateTaskOfficeCleaning]: {
    path: 'v5/update-task-my/office-cleaning',
  },
  [EndpointKeys.updateTaskWashingMachine]: {
    path: 'v5/update-task-my/washing-machine',
  },
  [EndpointKeys.updateTaskWaterHeater]: {
    path: 'v5/update-task-my/water-heater',
  },
  [EndpointKeys.updateTaskOfficeCarpetCleaning]: {
    path: 'v5/update-task-my/office-carpet-cleaning',
  },
  [EndpointKeys.updateTaskMassage]: {
    path: 'v5/update-task-my/massage',
  },
  [EndpointKeys.updateTaskIndustrialCleaning]: {
    path: 'v5/update-task-my/industrial-cleaning',
  },
  [EndpointKeys.updateTaskHomeMoving]: {
    path: 'v5/update-task-my/home-moving',
  },
  [EndpointKeys.updateTaskBeautyCare]: {
    path: 'v5/update-task-my/beauty-care',
  },
  [EndpointKeys.updateTaskIroning]: {
    path: 'v5/update-task-my/ironing',
  },
  [EndpointKeys.updateTaskHairStyling]: {
    path: 'v5/update-task-my/hair-styling',
  },
  [EndpointKeys.updateTaskMakeup]: {
    path: 'v5/update-task-my/makeup',
  },
  [EndpointKeys.updateTaskNail]: {
    path: 'v5/update-task-my/nail',
  },
  [EndpointKeys.updateGroceryAssistant]: {
    path: 'v5/update-task-my/grocery-assistant',
  },
  [EndpointKeys.checkTaskerConflictUpdateTime]: {
    path: 'v5/update-task-my/check-tasker-conflict-time',
  },
  [EndpointKeys.postTaskWashingMachine]: {
    path: 'v5/booking-my/washing-machine',
  },
  [EndpointKeys.postTaskDisinfection]: {
    path: 'v5/booking-my/disinfection',
  },
  [EndpointKeys.postTaskHomeCooking]: {
    path: 'v5/booking-my/home-cooking',
  },
  [EndpointKeys.postTaskOfficeCarpetCleaning]: {
    path: 'v5/booking-my/carpet-cleaning',
  },
  [EndpointKeys.postTaskIndustrialCleaning]: {
    path: 'v5/booking-my/industrial-cleaning',
  },
  [EndpointKeys.postTaskSofaCleaning]: {
    path: 'v5/booking-my/sofa',
  },
  [EndpointKeys.postTaskIroning]: {
    path: 'v5/booking-my/ironing',
  },
  [EndpointKeys.postTaskLaundry]: {
    path: 'v5/booking-my/laundry',
  },
  [EndpointKeys.postTaskHomeMoving]: {
    path: 'v5/booking-my/home-moving',
  },
  [EndpointKeys.getDetailSubscriptionSchedule]: {
    path: 'v5/api-asker-my/get-detail-subscription-schedule',
  },
  [EndpointKeys.getScheduleDetail]: {
    path: 'v5/api-asker-my/get-task-schedule-detail',
  },
  [EndpointKeys.activeTaskScheduleByTaskId]: {
    path: 'v5/api-asker-my/active-task-schedule-by-task-id',
  },
  [EndpointKeys.updateTaskScheduleTime]: {
    path: 'v5/api-asker-my/update-schedule-time',
  },
  [EndpointKeys.cancelTaskSchedule]: {
    path: 'v5/api-asker-my/remove-task-schedule',
  },
  [EndpointKeys.updateTaskSchedule]: {
    path: 'v5/api-asker-my/active-task-schedule',
  },

  /* -------------------------------- DUAL AUTH ------------------------------- */
  /* -------------- Public APIs accessible with or without login -------------- */
  [EndpointKeys.pricingHomeCleaning]: {
    path: 'v5/pricing-my/home-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceAirConditioner]: {
    path: 'v5/pricing-my/air-conditioner-v2',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceChildCare]: {
    path: 'v5/pricing-my/child-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceElderlyCare]: {
    path: 'v5/pricing-my/elderly-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPricePatientCare]: {
    path: 'v5/pricing-my/patient-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceOfficeCleaning]: {
    path: 'v5/pricing-my/office-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceCleaningSubscription]: {
    path: 'v5/pricing-my/subscription',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceCleaning]: {
    path: 'v5/pricing-my/home-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceChildCareSubscription]: {
    path: 'v5/pricing-my/subscription-child-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceOfficeCleaningSubscription]: {
    path: 'v5/pricing-my/subscription-office-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceElderlyCareSubscription]: {
    path: 'v5/pricing-my/subscription-elderly-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPricePatientCareSubscription]: {
    path: 'v5/pricing-my/subscription-patient-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceWaterHeater]: {
    path: 'v5/pricing-my/water-heater',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceWashingMachine]: {
    path: 'v5/pricing-my/washing-machine',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceDisinfection]: {
    path: 'v5/pricing-my/disinfection',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceHomeCooking]: {
    path: 'v5/pricing-my/home-cooking',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceOfficeCarpetCleaning]: {
    path: 'v5/pricing-my/carpet-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceIndustrialCleaning]: {
    path: 'v5/pricing-my/industrial-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceSofaCleaning]: {
    path: 'v5/pricing-my/sofa',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceMassage]: {
    path: 'v5/pricing-my/massage',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceIroning]: {
    path: 'v5/pricing-my/ironing',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceLaundry]: {
    path: 'v5/pricing-my/laundry',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceHomeMoving]: {
    path: 'v5/pricing-my/home-moving',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceHousekeeping]: {
    path: 'v5/pricing-my/housekeeping-v2',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceDeepCleaning]: {
    path: 'v5/pricing-my/deep-cleaning',
    isDualAuth: true,
  },
};
