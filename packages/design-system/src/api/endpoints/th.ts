import { EndpointConfig, EndpointKeys } from './type';

export const EndpointsTH: Record<EndpointKeys, EndpointConfig> = {
  [EndpointKeys.getEnv]: {
    path: 'v5/env-th/get-asker-env',
    isDualAuth: true,
  },
  [EndpointKeys.getAllSettingsWithoutLogin]: {
    path: 'v5/api-asker-th/get-all-settings-without-login',
  },
  [EndpointKeys.getAllSettings]: {
    path: 'v5/api-asker-th/get-all-settings',
  },
  [EndpointKeys.getUser]: {
    path: 'v5/api-asker-th/get-user',
  },
  [EndpointKeys.getUpComingTasks]: {
    path: 'v5/api-asker-th/get-up-coming-tasks',
  },
  [EndpointKeys.getScheduleTasks]: {
    path: 'v5/api-asker-th/get-schedule-tasks',
  },
  [EndpointKeys.getMonthlyTasks]: {
    path: 'v5/api-asker-th/get-subscription-by-userId',
  },
  [EndpointKeys.checkTaskSameTime]: {
    path: 'v5/api-asker-th/check-task-sametime',
  },
  [EndpointKeys.postTaskAirConditioner]: {
    path: 'v5/booking-th/air-conditioner',
  },
  [EndpointKeys.postTaskChildCare]: {
    path: 'v5/booking-th/child-care',
  },
  [EndpointKeys.postTaskElderlyCare]: {
    path: 'v5/booking-th/elderly-care',
  },
  [EndpointKeys.postTaskPatientCare]: {
    path: 'v5/booking-th/patient-care',
  },
  [EndpointKeys.postTaskOfficeCleaning]: {
    path: 'v5/booking-th/office-cleaning',
  },
  [EndpointKeys.checkTaskerConflictTime]: {
    path: 'v5/api-asker-th/check-tasker-conflict-time',
  },
  [EndpointKeys.postTaskSubscriptionCleaning]: {
    path: 'v5/booking-th/subscription',
  },
  [EndpointKeys.postTaskOfficeCleaningSubscription]: {
    path: 'v5/booking-th/subscription-office-cleaning',
  },
  [EndpointKeys.getFinancialAccount]: {
    path: 'v5/api-asker-th/get-financial-account',
  },
  [EndpointKeys.bookTaskForceTasker]: {
    path: 'v5/booking-th/book-task-force-tasker',
  },
  [EndpointKeys.postTaskCleaning]: {
    path: 'v5/booking-th/home-cleaning',
  },
  [EndpointKeys.getOutstandingPayment]: {
    path: 'v5/api-asker-th/get-outstanding-payment',
  },
  [EndpointKeys.getTaskDetail]: {
    path: 'v5/api-asker-th/get-task-detail',
  },
  [EndpointKeys.cancelTask]: {
    path: 'v5/cancel-task-th/cancel',
  },
  [EndpointKeys.addFavoriteService]: {
    path: 'v5/api-asker-th/add-favourite-services',
  },
  [EndpointKeys.postTaskWaterHeater]: {
    path: 'v5/booking-th/water-heater',
  },
  // Housekeeping
  [EndpointKeys.createHousekeepingLocation]: {
    path: 'v5/api-asker-th/add-housekeeping-location',
  },
  [EndpointKeys.updateRoomHousekeepingLocation]: {
    path: 'v5/api-asker-th/update-housekeeping-location',
  },
  [EndpointKeys.deleteRoomHousekeepingLocation]: {
    path: 'v5/api-asker-th/delete-housekeeping-location',
  },
  [EndpointKeys.postTaskHousekeeping]: {
    path: 'v5/booking-th/housekeeping-v2',
  },
  [EndpointKeys.postTaskDeepCleaning]: {
    path: 'v5/booking-th/deep-cleaning',
  },

  // Update task
  [EndpointKeys.changePremiumOption]: {
    path: 'v5/update-task-th/update-task-premium',
  },
  [EndpointKeys.updateTaskNote]: {
    path: 'v5/update-task-th/update-task-note',
  },
  [EndpointKeys.getHistoryTasks]: {
    path: 'v5/api-asker-th/get-list-history-tasks',
  },
  [EndpointKeys.getExtraMoneyUpdateDateTime]: {
    path: 'v5/update-task-th/get-extra-money-update-date-time',
  },
  [EndpointKeys.createUpdateDateTimeRequest]: {
    path: 'v5/update-task-th/create-update-date-time-request',
  },
  [EndpointKeys.askerCreateRequestUpdateDetailV4]: {
    path: 'v5/update-task-th/asker-create-request',
  },
  [EndpointKeys.updateTaskHomeCleaning]: {
    path: 'v5/update-task-th/home-cleaning',
  },
  [EndpointKeys.updateHouseKeeping]: {
    path: 'v5/update-task-th/house-keeping',
  },
  [EndpointKeys.updateTaskHomeCooking]: {
    path: 'v5/update-task-th/home-cooking',
  },
  [EndpointKeys.updateTaskDeepCleaning]: {
    path: 'v5/update-task-th/deep-cleaning',
  },
  [EndpointKeys.updateTaskAirConditioner]: {
    path: 'v5/update-task-th/air-conditioner',
  },
  [EndpointKeys.updateGroceryAssistant]: {
    path: 'v5/update-task-th/grocery-assistant',
  },
  [EndpointKeys.updateTaskSofa]: {
    path: 'v5/update-task-th/sofa',
  },
  [EndpointKeys.updateTaskElderlyCare]: {
    path: 'v5/update-task-th/elderly-care',
  },
  [EndpointKeys.updateTaskPatientCare]: {
    path: 'v5/update-task-th/patient-care',
  },
  [EndpointKeys.updateTaskDisinfection]: {
    path: 'v5/update-task-th/disinfection',
  },
  [EndpointKeys.updateTaskChildCare]: {
    path: 'v5/update-task-th/child-care',
  },
  [EndpointKeys.updateTaskOfficeCleaning]: {
    path: 'v5/update-task-th/office-cleaning',
  },
  [EndpointKeys.updateTaskWashingMachine]: {
    path: 'v5/update-task-th/washing-machine',
  },
  [EndpointKeys.updateTaskWaterHeater]: {
    path: 'v5/update-task-th/water-heater',
  },
  [EndpointKeys.updateTaskOfficeCarpetCleaning]: {
    path: 'v5/update-task-th/office-carpet-cleaning',
  },
  [EndpointKeys.updateTaskMassage]: {
    path: 'v5/update-task-th/massage',
  },
  [EndpointKeys.updateTaskIndustrialCleaning]: {
    path: 'v5/update-task-th/industrial-cleaning',
  },
  [EndpointKeys.updateTaskHomeMoving]: {
    path: 'v5/update-task-th/home-moving',
  },
  [EndpointKeys.updateTaskBeautyCare]: {
    path: 'v5/update-task-th/beauty-care',
  },
  [EndpointKeys.updateTaskIroning]: {
    path: 'v5/update-task-th/ironing',
  },
  [EndpointKeys.updateTaskHairStyling]: {
    path: 'v5/update-task-th/hair-styling',
  },
  [EndpointKeys.updateTaskMakeup]: {
    path: 'v5/update-task-th/makeup',
  },
  [EndpointKeys.updateTaskNail]: {
    path: 'v5/update-task-th/nail',
  },
  [EndpointKeys.checkTaskerConflictUpdateTime]: {
    path: 'v5/update-task-th/check-tasker-conflict-time',
  },
  [EndpointKeys.postTaskWashingMachine]: {
    path: 'v5/booking-th/washing-machine',
  },
  [EndpointKeys.postTaskDisinfection]: {
    path: 'v5/booking-th/disinfection',
  },
  [EndpointKeys.postTaskHomeCooking]: {
    path: 'v5/booking-th/home-cooking',
  },
  [EndpointKeys.postTaskOfficeCarpetCleaning]: {
    path: 'v5/booking-th/carpet-cleaning',
  },
  [EndpointKeys.postTaskIndustrialCleaning]: {
    path: 'v5/booking-th/industrial-cleaning',
  },
  [EndpointKeys.postTaskSofaCleaning]: {
    path: 'v5/booking-th/sofa',
  },
  [EndpointKeys.postTaskMassage]: {
    path: 'v5/booking-th/massage',
  },
  [EndpointKeys.postTaskIroning]: {
    path: 'v5/booking-th/ironing',
  },
  [EndpointKeys.postTaskLaundry]: {
    path: 'v5/booking-th/laundry',
  },
  [EndpointKeys.postTaskHomeMoving]: {
    path: 'v5/booking-th/home-moving',
  },
  [EndpointKeys.getDetailSubscriptionSchedule]: {
    path: 'v5/api-asker-th/get-detail-subscription-schedule',
  },
  [EndpointKeys.getScheduleDetail]: {
    path: 'v5/api-asker-th/get-task-schedule-detail',
  },
  [EndpointKeys.activeTaskScheduleByTaskId]: {
    path: 'v5/api-asker-th/active-task-schedule-by-task-id',
  },
  [EndpointKeys.updateTaskScheduleTime]: {
    path: 'v5/api-asker-th/update-schedule-time',
  },
  [EndpointKeys.cancelTaskSchedule]: {
    path: 'v5/api-asker-th/remove-task-schedule',
  },
  [EndpointKeys.updateTaskSchedule]: {
    path: 'v5/api-asker-th/active-task-schedule',
  },

  /* -------------------------------- DUAL AUTH ------------------------------- */
  /* -------------- Public APIs accessible with or without login -------------- */
  [EndpointKeys.pricingHomeCleaning]: {
    path: 'v5/pricing-th/home-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceAirConditioner]: {
    path: 'v5/pricing-th/air-conditioner-v2',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceChildCare]: {
    path: 'v5/pricing-th/child-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceElderlyCare]: {
    path: 'v5/pricing-th/elderly-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPricePatientCare]: {
    path: 'v5/pricing-th/patient-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceOfficeCleaning]: {
    path: 'v5/pricing-th/office-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceCleaningSubscription]: {
    path: 'v5/pricing-th/subscription',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceCleaning]: {
    path: 'v5/pricing-th/home-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceChildCareSubscription]: {
    path: 'v5/pricing-th/subscription-child-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceOfficeCleaningSubscription]: {
    path: 'v5/pricing-th/subscription-office-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceElderlyCareSubscription]: {
    path: 'v5/pricing-th/subscription-elderly-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPricePatientCareSubscription]: {
    path: 'v5/pricing-th/subscription-patient-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceWaterHeater]: {
    path: 'v5/pricing-th/water-heater',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceWashingMachine]: {
    path: 'v5/pricing-th/washing-machine',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceDisinfection]: {
    path: 'v5/pricing-th/disinfection',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceHomeCooking]: {
    path: 'v5/pricing-th/home-cooking',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceOfficeCarpetCleaning]: {
    path: 'v5/pricing-th/carpet-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceIndustrialCleaning]: {
    path: 'v5/pricing-th/industrial-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceSofaCleaning]: {
    path: 'v5/pricing-th/sofa',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceMassage]: {
    path: 'v5/pricing-th/massage',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceIroning]: {
    path: 'v5/pricing-th/ironing',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceLaundry]: {
    path: 'v5/pricing-th/laundry',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceHomeMoving]: {
    path: 'v5/pricing-th/home-moving',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceHousekeeping]: {
    path: 'v5/pricing-th/housekeeping-v2',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceDeepCleaning]: {
    path: 'v5/pricing-th/deep-cleaning',
    isDualAuth: true,
  },
};
