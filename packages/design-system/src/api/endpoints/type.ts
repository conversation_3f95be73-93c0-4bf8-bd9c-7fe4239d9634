export interface EndpointConfig {
  path: string;
  isDualAuth?: boolean;
}

export enum EndpointKeys {
  getEnv = 'getEnv',
  getAllSettingsWithoutLogin = 'getAllSettingsWithoutLogin',
  getAllSettings = 'getAllSettings',
  getUser = 'getUser',
  getUpComingTasks = 'getUpComingTasks',
  getScheduleTasks = 'getScheduleTasks',
  getMonthlyTasks = 'getMonthlyTasks',
  checkTaskSameTime = 'checkTaskSameTime',
  postTaskAirConditioner = 'postTaskAirConditioner',
  postTaskChildCare = 'postTaskChildCare',
  postTaskElderlyCare = 'postTaskElderlyCare',
  postTaskPatientCare = 'postTaskPatientCare',
  postTaskOfficeCleaning = 'postTaskOfficeCleaning',
  checkTaskerConflictTime = 'checkTaskerConflictTime',
  postTaskSubscriptionCleaning = 'postTaskSubscriptionCleaning',
  postTaskOfficeCleaningSubscription = 'postTaskOfficeCleaningSubscription',
  getFinancialAccount = 'getFinancialAccount',
  getCardList = 'getCardList',
  removeCard = 'removeCard',
  setCardDefault = 'setCardDefault',
  bookTaskForceTasker = 'bookTaskForceTasker',
  postTaskCleaning = 'postTaskCleaning',
  postTaskWaterHeater = 'postTaskWaterHeater',
  postTaskWashingMachine = 'postTaskWashingMachine',
  postTaskDisinfection = 'postTaskDisinfection',
  postTaskHomeCooking = 'postTaskHomeCooking',
  postTaskOfficeCarpetCleaning = 'postTaskOfficeCarpetCleaning',
  postTaskIndustrialCleaning = 'postTaskIndustrialCleaning',
  postTaskSofaCleaning = 'postTaskSofaCleaning',
  postTaskMassage = 'postTaskMassage',
  postTaskIroning = 'postTaskIroning',
  postTaskLaundry = 'postTaskLaundry',
  getOutstandingPayment = 'getOutstandingPayment',
  getTaskDetail = 'getTaskDetail',
  cancelTask = 'cancelTask',
  addFavoriteService = 'addFavoriteService',
  postTaskHomeMoving = 'postTaskHomeMoving',
  postTaskDeepCleaning = 'postTaskDeepCleaning',
  // update task
  changePremiumOption = 'changePremiumOption',
  updateTaskNote = 'updateTaskNote',
  getHistoryTasks = 'getHistoryTasks',
  checkTaskerConflictUpdateTime = 'checkTaskerConflictUpdateTime',
  getExtraMoneyUpdateDateTime = 'getExtraMoneyUpdateDateTime',
  createUpdateDateTimeRequest = 'createUpdateDateTimeRequest',
  askerCreateRequestUpdateDetailV4 = 'askerCreateRequestUpdateDetailV4',
  updateTaskHomeCleaning = 'updateTaskHomeCleaning',
  updateHouseKeeping = 'updateHouseKeeping',
  updateTaskHomeCooking = 'updateTaskHomeCooking',
  updateTaskDeepCleaning = 'updateTaskDeepCleaning',
  updateTaskAirConditioner = 'updateTaskAirConditioner',
  updateGroceryAssistant = 'updateGroceryAssistant',
  updateTaskSofa = 'updateTaskSofa',
  updateTaskElderlyCare = 'updateTaskElderlyCare',
  updateTaskPatientCare = 'updateTaskPatientCare',
  updateTaskDisinfection = 'updateTaskDisinfection',
  updateTaskChildCare = 'updateTaskChildCare',
  updateTaskOfficeCleaning = 'updateTaskOfficeCleaning',
  updateTaskWashingMachine = 'updateTaskWashingMachine',
  updateTaskWaterHeater = 'updateTaskWaterHeater',
  updateTaskOfficeCarpetCleaning = 'updateTaskOfficeCarpetCleaning',
  updateTaskMassage = 'updateTaskMassage',
  updateTaskIndustrialCleaning = 'updateTaskIndustrialCleaning',
  updateTaskHomeMoving = 'updateTaskHomeMoving',
  updateTaskBeautyCare = 'updateTaskBeautyCare',
  updateTaskIroning = 'updateTaskIroning',
  updateTaskHairStyling = 'updateTaskHairStyling',
  updateTaskMakeup = 'updateTaskMakeup',
  updateTaskNail = 'updateTaskNail',
  getDetailSubscriptionSchedule = 'getDetailSubscriptionSchedule',
  // Housekeeping
  createHousekeepingLocation = 'createHousekeepingLocation',
  updateRoomHousekeepingLocation = 'updateRoomHousekeepingLocation',
  deleteRoomHousekeepingLocation = 'deleteRoomHousekeepingLocation',
  postTaskHousekeeping = 'postTaskHousekeeping',
  getScheduleDetail = 'getScheduleDetail',
  activeTaskScheduleByTaskId = 'activeTaskScheduleByTaskId',
  updateTaskScheduleTime = 'updateTaskScheduleTime',
  cancelTaskSchedule = 'cancelTaskSchedule',
  updateTaskSchedule = 'updateTaskSchedule',

  /* -------------------------------- DUAL AUTH ------------------------------- */
  /* -------------- Public APIs accessible with or without login -------------- */
  pricingHomeCleaning = 'pricingHomeCleaning',
  getPriceAirConditioner = 'getPriceAirConditioner',
  getPriceChildCare = 'getPriceChildCare',
  getPriceElderlyCare = 'getPriceElderlyCare',
  getPricePatientCare = 'getPricePatientCare',
  getPriceOfficeCleaning = 'getPriceOfficeCleaning',
  getPriceCleaningSubscription = 'getPriceCleaningSubscription',
  getPriceChildCareSubscription = 'getPriceChildCareSubscription',
  getPriceElderlyCareSubscription = 'getPriceElderlyCareSubscription',
  getPricePatientCareSubscription = 'getPricePatientCareSubscription',
  getPriceCleaning = 'getPriceCleaning',
  getPriceOfficeCleaningSubscription = 'getPriceOfficeCleaningSubscription',
  getPriceWaterHeater = 'getPriceWaterHeater',
  getPriceWashingMachine = 'getPriceWashingMachine',
  getPriceDisinfection = 'getPriceDisinfection',
  getPriceHomeCooking = 'getPriceHomeCooking',
  getPriceOfficeCarpetCleaning = 'getPriceOfficeCarpetCleaning',
  getPriceIndustrialCleaning = 'getPriceIndustrialCleaning',
  getPriceSofaCleaning = 'getPriceSofaCleaning',
  getPriceMassage = 'getPriceMassage',
  getPriceIroning = 'getPriceIroning',
  getPriceLaundry = 'getPriceLaundry',
  getPriceHomeMoving = 'getPriceHomeMoving',
  getPriceHousekeeping = 'getPriceHousekeeping',
  getPriceDeepCleaning = 'getPriceDeepCleaning',
}
