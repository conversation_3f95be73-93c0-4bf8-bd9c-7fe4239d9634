import { EndpointConfig, EndpointKeys } from './type';

export const EndpointsVN: Record<EndpointKeys, EndpointConfig> = {
  [EndpointKeys.getEnv]: {
    path: 'v5/env-vn/get-asker-env',
  },
  [EndpointKeys.getAllSettingsWithoutLogin]: {
    path: 'v5/api-asker-vn/get-all-settings-without-login',
  },
  [EndpointKeys.getAllSettings]: {
    path: 'v5/api-asker-vn/get-all-settings',
  },
  [EndpointKeys.getUser]: {
    path: 'v5/api-asker-vn/get-user',
  },
  [EndpointKeys.getUpComingTasks]: {
    path: 'v5/api-asker-vn/get-up-coming-tasks',
  },
  [EndpointKeys.getScheduleTasks]: {
    path: 'v5/api-asker-vn/get-schedule-tasks',
  },
  [EndpointKeys.getMonthlyTasks]: {
    path: 'v5/api-asker-vn/get-subscription-by-userId',
  },
  [EndpointKeys.checkTaskSameTime]: {
    path: 'v5/api-asker-vn/check-task-sametime',
  },
  [EndpointKeys.postTaskAirConditioner]: {
    path: 'v5/booking/air-conditioner',
  },
  [EndpointKeys.postTaskChildCare]: {
    path: 'v5/booking/child-care',
  },
  [EndpointKeys.postTaskElderlyCare]: {
    path: 'v5/booking/elderly-care',
  },
  [EndpointKeys.postTaskPatientCare]: {
    path: 'v5/booking/patient-care',
  },
  [EndpointKeys.postTaskOfficeCleaning]: {
    path: 'v5/booking/office-cleaning',
  },
  [EndpointKeys.checkTaskerConflictTime]: {
    path: 'v5/api-asker-vn/check-tasker-conflict-time',
  },
  [EndpointKeys.postTaskSubscriptionCleaning]: {
    path: 'v5/booking/subscription',
  },
  [EndpointKeys.getFinancialAccount]: {
    path: 'v5/api-asker-vn/get-financial-account',
  },
  [EndpointKeys.bookTaskForceTasker]: {
    path: 'v5/booking/book-task-force-tasker',
  },
  [EndpointKeys.postTaskCleaning]: {
    path: 'v5/booking/home-cleaning',
  },
  [EndpointKeys.postTaskOfficeCleaningSubscription]: {
    path: 'v5/booking/subscription-office-cleaning',
  },
  // Card
  [EndpointKeys.getCardList]: {
    path: 'v5/api-asker-vn/get-list-payment',
  },
  [EndpointKeys.removeCard]: {
    path: 'v5/payment/disable-card',
  },
  [EndpointKeys.setCardDefault]: {
    path: 'v5/api-asker-vn/set-payment-card-default',
  },
  [EndpointKeys.getOutstandingPayment]: {
    path: 'v5/api-asker-vn/get-outstanding-payment',
  },
  [EndpointKeys.getTaskDetail]: {
    path: 'v5/api-asker-vn/get-task-detail',
  },
  [EndpointKeys.cancelTask]: {
    path: 'v5/cancel-task-vn/cancel',
  },
  // End card
  [EndpointKeys.addFavoriteService]: {
    path: 'v5/api-asker-vn/add-favourite-services',
  },
  [EndpointKeys.postTaskWaterHeater]: {
    path: 'v5/booking/water-heater',
  },
  // Housekeeping
  [EndpointKeys.createHousekeepingLocation]: {
    path: 'v5/api-asker-vn/add-housekeeping-location',
  },
  [EndpointKeys.updateRoomHousekeepingLocation]: {
    path: 'v5/api-asker-vn/update-housekeeping-location',
  },
  [EndpointKeys.deleteRoomHousekeepingLocation]: {
    path: 'v5/api-asker-vn/delete-housekeeping-location',
  },
  [EndpointKeys.postTaskHousekeeping]: {
    path: 'v5/booking/housekeeping-v2',
  },
  [EndpointKeys.postTaskDeepCleaning]: {
    path: 'v5/booking/deep-cleaning',
  },

  // Update task
  [EndpointKeys.changePremiumOption]: {
    path: 'v5/update-task-vn/update-task-premium',
  },
  [EndpointKeys.updateTaskNote]: {
    path: 'v5/update-task-vn/update-task-note',
  },
  [EndpointKeys.getHistoryTasks]: {
    path: 'v5/api-asker-vn/get-list-history-tasks',
  },
  [EndpointKeys.getExtraMoneyUpdateDateTime]: {
    path: 'v5/update-task-vn/get-extra-money-update-date-time',
  },
  [EndpointKeys.createUpdateDateTimeRequest]: {
    path: 'v5/update-task-vn/asker-create-update-date-time-request',
  },
  [EndpointKeys.askerCreateRequestUpdateDetailV4]: {
    path: 'v5/update-task-vn/asker-create-request',
  },
  [EndpointKeys.updateTaskHomeCleaning]: {
    path: 'v5/update-task-vn/home-cleaning',
  },
  [EndpointKeys.updateHouseKeeping]: {
    path: 'v5/update-task-vn/house-keeping',
  },
  [EndpointKeys.updateTaskHomeCooking]: {
    path: 'v5/update-task-vn/home-cooking',
  },
  [EndpointKeys.updateTaskDeepCleaning]: {
    path: 'v5/update-task-vn/deep-cleaning',
  },
  [EndpointKeys.updateTaskAirConditioner]: {
    path: 'v5/update-task-vn/air-conditioner',
  },
  [EndpointKeys.updateGroceryAssistant]: {
    path: 'v5/update-task-vn/grocery-assistant',
  },
  [EndpointKeys.updateTaskSofa]: {
    path: 'v5/update-task-vn/sofa',
  },
  [EndpointKeys.updateTaskElderlyCare]: {
    path: 'v5/update-task-vn/elderly-care',
  },
  [EndpointKeys.updateTaskPatientCare]: {
    path: 'v5/update-task-vn/patient-care',
  },
  [EndpointKeys.updateTaskDisinfection]: {
    path: 'v5/update-task-vn/disinfection',
  },
  [EndpointKeys.updateTaskChildCare]: {
    path: 'v5/update-task-vn/child-care',
  },
  [EndpointKeys.updateTaskOfficeCleaning]: {
    path: 'v5/update-task-vn/office-cleaning',
  },
  [EndpointKeys.updateTaskWashingMachine]: {
    path: 'v5/update-task-vn/washing-machine',
  },
  [EndpointKeys.updateTaskWaterHeater]: {
    path: 'v5/update-task-vn/water-heater',
  },
  [EndpointKeys.updateTaskOfficeCarpetCleaning]: {
    path: 'v5/update-task-vn/office-carpet-cleaning',
  },
  [EndpointKeys.updateTaskMassage]: {
    path: 'v5/update-task-vn/massage',
  },
  [EndpointKeys.updateTaskIndustrialCleaning]: {
    path: 'v5/update-task-vn/industrial-cleaning',
  },
  [EndpointKeys.updateTaskHomeMoving]: {
    path: 'v5/update-task-vn/home-moving',
  },
  [EndpointKeys.updateTaskBeautyCare]: {
    path: 'v5/update-task-vn/beauty-care',
  },
  [EndpointKeys.updateTaskIroning]: {
    path: 'v5/update-task-vn/ironing',
  },
  [EndpointKeys.updateTaskHairStyling]: {
    path: 'v5/update-task-vn/hair-styling',
  },
  [EndpointKeys.updateTaskMakeup]: {
    path: 'v5/update-task-vn/makeup',
  },
  [EndpointKeys.updateTaskNail]: {
    path: 'v5/update-task-vn/nail',
  },
  [EndpointKeys.checkTaskerConflictUpdateTime]: {
    path: 'v5/update-task-vn/check-tasker-conflict-time',
  },
  [EndpointKeys.postTaskWashingMachine]: {
    path: 'v5/booking/washing-machine',
  },
  [EndpointKeys.postTaskDisinfection]: {
    path: 'v5/booking/disinfection',
  },
  [EndpointKeys.postTaskHomeCooking]: {
    path: 'v5/booking/home-cooking',
  },
  [EndpointKeys.postTaskOfficeCarpetCleaning]: {
    path: 'v5/booking/carpet-cleaning',
  },
  [EndpointKeys.postTaskIndustrialCleaning]: {
    path: 'v5/booking/industrial-cleaning',
  },
  [EndpointKeys.postTaskSofaCleaning]: {
    path: 'v5/booking/sofa',
  },
  [EndpointKeys.postTaskMassage]: {
    path: 'v5/booking/massage',
  },
  [EndpointKeys.postTaskIroning]: {
    path: 'v5/booking/ironing',
  },
  [EndpointKeys.postTaskLaundry]: {
    path: 'v5/booking/laundry',
  },
  [EndpointKeys.postTaskHomeMoving]: {
    path: 'v5/booking/home-moving',
  },
  [EndpointKeys.getDetailSubscriptionSchedule]: {
    path: 'v5/api-asker-vn/get-detail-subscription-schedule',
  },
  [EndpointKeys.getScheduleDetail]: {
    path: 'v5/api-asker-vn/get-task-schedule-detail',
  },
  [EndpointKeys.activeTaskScheduleByTaskId]: {
    path: 'v5/api-asker-vn/active-task-schedule-by-task-id',
  },
  [EndpointKeys.updateTaskScheduleTime]: {
    path: 'v5/api-asker-vn/update-schedule-time',
  },
  [EndpointKeys.cancelTaskSchedule]: {
    path: 'v5/api-asker-vn/remove-task-schedule',
  },
  [EndpointKeys.updateTaskSchedule]: {
    path: 'v5/api-asker-vn/active-task-schedule',
  },

  /* -------------------------------- DUAL AUTH ------------------------------- */
  /* -------------- Public APIs accessible with or without login -------------- */
  [EndpointKeys.pricingHomeCleaning]: {
    path: 'v5/pricing-vn/home-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceAirConditioner]: {
    path: 'v5/pricing-vn/air-conditioner-v2',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceChildCare]: {
    path: 'v5/pricing-vn/child-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceElderlyCare]: {
    path: 'v5/pricing-vn/elderly-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPricePatientCare]: {
    path: 'v5/pricing-vn/patient-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceOfficeCleaning]: {
    path: 'v5/pricing-vn/office-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceCleaningSubscription]: {
    path: 'v5/pricing-vn/subscription',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceCleaning]: {
    path: 'v5/pricing-vn/home-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceChildCareSubscription]: {
    path: 'v5/pricing-vn/subscription-child-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceOfficeCleaningSubscription]: {
    path: 'v5/pricing-vn/subscription-office-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceElderlyCareSubscription]: {
    path: 'v5/pricing-vn/subscription-elderly-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPricePatientCareSubscription]: {
    path: 'v5/pricing-vn/subscription-patient-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceWaterHeater]: {
    path: 'v5/pricing-vn/water-heater',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceWashingMachine]: {
    path: 'v5/pricing-vn/washing-machine',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceDisinfection]: {
    path: 'v5/pricing-vn/disinfection',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceHomeCooking]: {
    path: 'v5/pricing-vn/home-cooking',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceOfficeCarpetCleaning]: {
    path: 'v5/pricing-vn/carpet-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceIndustrialCleaning]: {
    path: 'v5/pricing-vn/industrial-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceSofaCleaning]: {
    path: 'v5/pricing-vn/sofa',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceMassage]: {
    path: 'v5/pricing-vn/massage',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceIroning]: {
    path: 'v5/pricing-vn/ironing',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceLaundry]: {
    path: 'v5/pricing-vn/laundry',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceHomeMoving]: {
    path: 'v5/pricing-vn/home-moving',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceHousekeeping]: {
    path: 'v5/pricing-vn/housekeeping-v2',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceDeepCleaning]: {
    path: 'v5/pricing-vn/deep-cleaning',
    isDualAuth: true,
  },
};
