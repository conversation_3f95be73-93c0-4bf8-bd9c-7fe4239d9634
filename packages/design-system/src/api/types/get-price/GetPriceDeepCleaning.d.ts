import { IAddons, IPrice, ITaskPlace } from '../../../types';

export interface IParamsGetPriceDeepCleaning {
  task: {
    timezone?: string;
    date: string;
    autoChooseTasker: boolean;
    taskPlace: ITaskPlace;
    homeType?: string;
    duration: number;
    payment?: {
      method: string;
    };
    requirements?: { type: number }[];
    addons?: IAddons[];
    isPremium?: boolean;
    detailDeepCleaning?: {
      area: number;
      numberOfTasker: number;
    };
  };
  service: {
    _id: string;
  };
  isoCode: string;
}

export interface IGetPriceDeepCleaningAPI {
  params?: IParamsGetPriceDeepCleaning;
  response?: IPrice;
  error?: any;
}
