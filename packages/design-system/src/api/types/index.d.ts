import { EndpointKeys } from '../endpoints';
import { EndpointKeys } from './../endpoints/type';
import { ICancelTaskAPI } from './CancelTask';
import { ICardList } from './Card';
import { ICheckTaskerConflictTimeAPI } from './CheckTaskerConflictTime';
import { ICheckTaskSameTimeAPI } from './CheckTaskSameTime';
import { IGetPriceAirConditionerAPI } from './get-price/GetPriceAirConditioner';
import { IGetPriceChildCareAPI } from './get-price/GetPriceChildCare';
import { IGetPriceCleaningAPI } from './get-price/GetPriceCleaning';
import { IGetPriceCleaningSubscriptionAPI } from './get-price/GetPriceCleaningSubscription';
import { IGetPriceDeepCleaningAPI } from './get-price/GetPriceDeepCleaning';
import { IGetPriceHomeMovingAPI } from './get-price/GetPriceHomeMoving';
import { IGetPriceMassageAPI } from './get-price/GetPriceMassage';
import { IGetPriceOfficeCleaningAPI } from './get-price/GetPriceOfficeCleaning';
import { IGetPriceOfficeCleaningSubscriptionAPI } from './get-price/GetPriceOfficeCleaningSubscription';
import { IGetPricePatientCareAPI } from './get-price/GetPricePatientCare';
import { IGetMonthlyTasks } from './get-task/GetMonthyTasks';
import { IGetScheduleTasks } from './get-task/GetScheduleTasks';
import { IGetTaskDetailAPI } from './get-task/GetTaskDetail';
import { IGetUpComingTasks } from './get-task/GetUpComingTasks';
import { IGetEnvAPI } from './GetEnv';
import { IGetFinancialAccountAPI } from './GetFinancialAccount';
import { IGetOutstandingPaymentAPI } from './GetOutstandingPayment';
import { IGetPriceAirConditionerAPI } from './GetPriceAirConditioner';
import { IGetPriceChildCareAPI } from './GetPriceChildCare';
import { IGetPriceHousekeepingAPI } from './GetPriceHousekeeping';
import { IGetSettingsAPI } from './GetSettings';
import { ICreateHousekeepingHostelAPI } from './hostel/create-housekeeping-hostel';
import { IDeleteRoomHousekeepingHostelAPI } from './hostel/delete-room-housekeeping-hostel';
import { IUpdateRoomHousekeepingHostelAPI } from './hostel/update-room-housekeeping-hostel';
import { IPostTaskAirConditionerAPI } from './post-task/PostTaskAirConditioner';
import { IPostTaskChildCareAPI } from './post-task/PostTaskChildCare';
import { IPostTaskCleaningAPI } from './post-task/PostTaskCleaning';
import { IPostTaskCleaningSubscriptionAPI } from './post-task/PostTaskCleaningSubscription';
import { IPostTaskDeepCleaningAPI } from './post-task/PostTaskDeepCleaning';
import { IPostTaskForceTaskerAPI } from './post-task/PostTaskForceTasker';
import { IPostTaskHomeMovingAPI } from './post-task/PostTaskHomeMoving';
import { IPostTaskHousekeepingAPI } from './post-task/PostTaskHousekeeping';
import { IPostTaskMassageAPI } from './post-task/PostTaskMassage';
import { IPostTaskOfficeCleaningAPI } from './post-task/PostTaskOfficeCleaning';
import { IPostTaskOfficeCleaningSubscriptionAPI } from './post-task/PostTaskOfficeCleaningSubscription';
import { IPostTaskPatientCareAPI } from './post-task/PostTaskPatientCare';
import { ICheckTaskerConflictUpdateTimeAPI } from './update-task/CheckTaskerConflictUpdateTime.d';
import { ICreateRequestUpdateDateTimeAPI } from './update-task/CreateRequestUpdateDateTime';
import { ICreateRequestUpdateDetailV4API } from './update-task/CreateRequestUpdateDetailV4';
import { IGetExtraMoneyUpdateDateTimeAPI } from './update-task/GetExtraMoneyUpdateDateTime';
import { IUpdatePremiumOptionAPI } from './update-task/UpdatePremiumOption';
import { IUpdateTaskDetailAPI } from './update-task/UpdateTaskDetail';
import { IUpdateTaskNoteAPI } from './update-task/UpdateTaskNote';

export interface ApiType {
  [EndpointKeys.getEnv]: IGetEnvAPI;
  [EndpointKeys.getAllSettingsWithoutLogin]: IGetSettingsAPI;
  [EndpointKeys.getAllSettings]: IGetSettingsAPI;
  [EndpointKeys.getUser]: IGetEnvAPI;
  [EndpointKeys.getUpComingTasks]: IGetUpComingTasks;
  [EndpointKeys.getScheduleTasks]: IGetScheduleTasks;
  [EndpointKeys.getMonthlyTasks]: IGetMonthlyTasks;
  [EndpointKeys.checkTaskSameTime]: ICheckTaskSameTimeAPI;
  [EndpointKeys.checkTaskerConflictTime]: ICheckTaskerConflictTimeAPI;
  [EndpointKeys.bookTaskForceTasker]: IPostTaskForceTaskerAPI;
  [EndpointKeys.getOutstandingPayment]: IGetOutstandingPaymentAPI;
  [EndpointKeys.getTaskDetail]: IGetTaskDetailAPI;
  [EndpointKeys.cancelTask]: ICancelTaskAPI;

  /* ------------------------- SERVICE AIR CONDITIONER ------------------------ */
  [EndpointKeys.postTaskAirConditioner]: IPostTaskAirConditionerAPI;
  [EndpointKeys.getPriceAirConditioner]: IGetPriceAirConditionerAPI;
  /* ----------------------- END SERVICE AIR CONDITIONER ---------------------- */

  /* --------------------------- SERVICE CHILD CARE --------------------------- */
  [EndpointKeys.postTaskChildCare]: IPostTaskChildCareAPI;
  [EndpointKeys.getPriceChildCare]: IGetPriceChildCareAPI;
  /* ------------------------- END SERVICE CHILD CARE ------------------------- */

  /* -------------------------- SERVICE PATIENT CARE -------------------------- */
  [EndpointKeys.postTaskPatientCare]: IPostTaskPatientCareAPI;
  [EndpointKeys.getPricePatientCare]: IGetPricePatientCareAPI;
  /* ------------------------ END SERVICE PATIENT CARE ------------------------ */

  /* ------------------------- SERVICE OFFICE CLEANING ------------------------ */
  [EndpointKeys.postTaskOfficeCleaning]: IPostTaskOfficeCleaningAPI;
  [EndpointKeys.getPriceOfficeCleaning]: IGetPriceOfficeCleaningAPI;
  [EndpointKeys.getPriceOfficeCleaningSubscription]: IGetPriceOfficeCleaningSubscriptionAPI;
  [EndpointKeys.postTaskOfficeCleaningSubscription]: IPostTaskOfficeCleaningSubscriptionAPI;
  /* ----------------------- END SERVICE OFFICE CLEANING ---------------------- */

  [EndpointKeys.pricingHomeCleaning]: IGetEnvAPI;

  /* ----------------------- SERVICE CLEANING SUBSCRIPTION ---------------------- */
  [EndpointKeys.getPriceCleaningSubscription]: IGetPriceCleaningSubscriptionAPI;
  [EndpointKeys.postTaskCleaningSubscription]: IPostTaskCleaningSubscriptionAPI;
  [EndpointKeys.getFinancialAccount]: IGetFinancialAccountAPI;

  /* ----------------------- SERVICE CARD ---------------------- */
  [EndpointKeys.getListPayment]: ICardList;
  [EndpointKeys.removePayment]: ICardList;
  [EndpointKeys.setPaymentDefault]: ICardList;
  /* ----------------------- END SERVICE CARD ---------------------- */

  /* ----------------------- SERVICE CLEANING ---------------------- */
  [EndpointKeys.getPriceCleaning]: IGetPriceCleaningAPI;
  [EndpointKeys.postTaskCleaning]: IPostTaskCleaningAPI;
  /* ----------------------- END SERVICE CLEANING ---------------------- */

  /* ----------------------- SERVICE MASSAGE ---------------------- */
  [EndpointKeys.getPriceMassage]: IGetPriceMassageAPI;
  [EndpointKeys.postTaskMassage]: IPostTaskMassageAPI;
  /* ----------------------- END SERVICE MASSAGE ---------------------- */

  /* ----------------------- SERVICE HOME MOVING ---------------------- */
  [EndpointKeys.getPriceHomeMoving]: IGetPriceHomeMovingAPI;
  [EndpointKeys.postTaskHomeMoving]: IPostTaskHomeMovingAPI;
  /* ----------------------- END SERVICE HOME MOVING ---------------------- */

  /* ----------------------- HOUSEKEEPING ---------------------- */
  [EndpointKeys.createHousekeepingLocation]: ICreateHousekeepingHostelAPI;
  [EndpointKeys.updateRoomHousekeepingLocation]: IUpdateRoomHousekeepingHostelAPI;
  [EndpointKeys.deleteRoomHousekeepingLocation]: IDeleteRoomHousekeepingHostelAPI;
  [EndpointKeys.getPriceHousekeeping]: IGetPriceHousekeepingAPI;
  [EndpointKeys.postTaskHousekeeping]: IPostTaskHousekeepingAPI;
  /* ----------------------- END HOUSEKEEPING ---------------------- */

  /* ----------------------- DEEP CLEANING ---------------------- */
  [EndpointKeys.getPriceDeepCleaning]: IGetPriceDeepCleaningAPI;
  [EndpointKeys.postTaskDeepCleaning]: IPostTaskDeepCleaningAPI;
  /* ----------------------- END DEEP CLEANING ---------------------- */

  /* ----------------------- UPDATE TASK ---------------------- */
  [EndpointKeys.changePremiumOption]: IUpdatePremiumOptionAPI;
  [EndpointKeys.updateTaskNote]: IUpdateTaskNoteAPI;
  [EndpointKeys.ICheckTaskerConflictUpdateTimeAPI]: ICheckTaskerConflictUpdateTimeAPI;
  [EndpointKeys.getExtraMoneyUpdateDateTime]: IGetExtraMoneyUpdateDateTimeAPI;
  [EndpointKeys.createUpdateDateTimeRequest]: ICreateRequestUpdateDateTimeAPI;
  [EndpointKeys.askerCreateRequestUpdateDetailV4]: ICreateRequestUpdateDetailV4API;
  [EndpointKeys.updateTaskHomeCleaning]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskAirConditioner]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskDeepCleaning]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskChildCare]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskHomeCooking]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskDisinfection]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskPatientCare]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskElderlyCare]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskSofa]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskOfficeCleaning]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskWashingMachine]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskWaterHeater]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskOfficeCarpetCleaning]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskMassage]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskIndustrialCleaning]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskHomeMoving]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskBeautyCare]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskIroning]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskHairStyling]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskMakeup]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateTaskNail]: IUpdateTaskDetailAPI;
  [EndpointKeys.updateGroceryAssistant]: IUpdateTaskDetailAPI;
  /* ----------------------- END UPDATE TASK ---------------------- */
}
