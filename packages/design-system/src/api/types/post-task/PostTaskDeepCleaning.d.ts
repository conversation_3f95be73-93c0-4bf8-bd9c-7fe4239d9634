import { IDate } from '../../../helpers';
import { IAddons, IDeviceInfo, Maybe } from '../../../types';
import { HomeType, ISO_CODE } from '../../../utils';

export type IDataBookingCleaning = {
  address?: string;
  homeNumber?: string;
  homeType?: HomeType;
  // Additional properties
  contactName?: string;
  lat?: number;
  lng?: number;
  phone?: string;
  countryCode?: string;
  description?: string;
  askerId?: string;
  autoChooseTasker?: boolean;
  date?: IDate;
  timezone?: string;
  deviceInfo?: IDeviceInfo;
  duration?: number;
  houseNumber?: string;
  isSendToFavTaskers?: boolean;
  isoCode?: Maybe<ISO_CODE>;
  payment?: {
    method?: string;
  };
  serviceId?: string;
  taskPlace: {
    city?: string;
    country?: string;
    district?: string;
    isAddressMaybeWrong?: boolean;
  };
  updateTaskNoteToUser?: boolean;
  shortAddress?: string;
  isTetBooking?: boolean;
  taskNote?: string;
  requirements?: {
    type: number;
  }[];
  pet?: any;
  promotion?: any;
  isPremium?: boolean;
  gender?: string;
  addons?: IAddons[];
  source?: {
    from?: string;
    taskId?: string;
  };
  detailDeepCleaning?: {
    numberOfTaskersDeepCleaning?: number;
    areaDeepCleaning?: number;
  };
};

export type IResponsePostTaskDeepCleaning = {
  bookingId?: string;
  isPrepayment?: boolean;
};

export class IPostTaskDeepCleaningAPI {
  params?: IDataBookingCleaning;
  response?: IResponsePostTaskDeepCleaning;
  error?: any;
}
