import { create } from 'zustand';

import { IDate, ITimezone } from '../../helpers';
import {
  I<PERSON>ddons,
  IAddress,
  IDataBooking,
  IPrice,
  IRelatedTask,
  IService,
  IUser,
  Maybe,
  Requirement,
} from '../../types';
import { ISO_CODE } from '../../utils';

interface AppState {
  address: IAddress;
  duration: number;
  requirements: Requirement[];
  isPremium: boolean;
  isAutoChooseTasker: boolean;
  isFavouriteTasker: boolean;
  gender: string;
  pet: any;
  addons: IAddons[];
  date: IDate | null;
  schedule: number[];
  isEnabledSchedule: boolean;
  note: string;
  isApplyNoteForAllTask: boolean;
  price: IPrice | null;
  service: Maybe<IService>;
  forceTasker: IUser;
  isoCode: ISO_CODE;
  dataQuickPostTask: IDataBooking | null;
  currency: {
    sign: string;
    code: string;
  } | null;
  dateOptions: IDate[];
  timezone: ITimezone;
  paymentMethod: any;
  promotion: any;
  loadingPrice: boolean;
  loadingPostTask: boolean;
  relatedTask: IRelatedTask | null;
  user: IUser | null;
  setAddress: (address: IAddress) => void;
  setDuration: (duration: number) => void;
  setRequirements: (requirements: Requirement[]) => void;
  setIsPremium: (isPremium: boolean) => void;
  setIsAutoChooseTasker: (isAutoChooseTasker: boolean) => void;
  setIsFavouriteTasker: (isFavouriteTasker: boolean) => void;
  setGender: (gender: string) => void;
  setAddons: (addons: IAddons[]) => void;
  setPet: (pet: any) => void;
  setDateTime: (date: IDate) => void;
  setSchedule: (schedule: number[]) => void;
  setIsEnabledSchedule: (isEnabledSchedule: boolean) => void;
  setNote: (note: string) => void;
  setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) => void;
  setPrice: (price: IPrice | null) => void;
  homeNumber: string;
  setHomeNumber: (homeNumber: string) => void;
  setPaymentMethod: (paymentMethod: any) => void;
  setPromotion: (promotion: any) => void;
  setLoadingPrice: (loadingPrice: boolean) => void;
  setLoadingPostTask: (loadingPostTask: boolean) => void;
  setRelatedTask: (relatedTask: IRelatedTask) => void;
  setUser: (user: IUser) => void;
  setService: (service: Maybe<IService>) => void;
  setCurrency: (currency: { sign: string; code: string }) => void;
  reset: () => void;
}

export const usePostTaskStore = create<AppState>((set) => ({
  address: {},
  duration: 0,
  requirements: [],
  isPremium: false,
  isAutoChooseTasker: true,
  isFavouriteTasker: false,
  gender: '',
  pet: '',
  addons: [],
  date: null,
  schedule: [],
  isEnabledSchedule: false,
  note: '',
  isApplyNoteForAllTask: false,
  dateOptions: [],
  homeNumber: '',
  price: null,
  service: null,
  timezone: 'Asia/Ho_Chi_Minh',
  forceTasker: {},
  isoCode: ISO_CODE.VN,
  dataQuickPostTask: null,
  currency: null,
  paymentMethod: {
    name: 'Cash',
    label: 'PAYMENT_METHOD_DIRECT_CASH',
    value: 'CASH',
    icon: require('../../assets/images/logo-cash.png'),
  },
  promotion: null,
  loadingPrice: false,
  loadingPostTask: false,
  relatedTask: null,
  user: null,
  setTimezone: (timezone: string) => set({ timezone: timezone }),
  setAddress: (address: IAddress) => set({ address: address }),
  setDuration: (duration: number) => set({ duration: duration }),
  setRequirements: (requirements: Requirement[]) =>
    set({ requirements: requirements }),
  setIsPremium: (isPremium: boolean) => set({ isPremium: isPremium }),
  setIsAutoChooseTasker: (isAutoChooseTasker: boolean) =>
    set({ isAutoChooseTasker: isAutoChooseTasker }),
  setIsFavouriteTasker: (isFavouriteTasker: boolean) =>
    set({ isFavouriteTasker: isFavouriteTasker }),
  setGender: (gender: string) => set({ gender: gender }),
  setAddons: (addons: IAddons[]) => set({ addons: addons }),
  setPet: (pet: any) => set({ pet: pet }),
  setDateTime: (date: IDate) => set({ date: date }),
  setSchedule: (schedule: number[]) => set({ schedule: schedule }),
  setIsEnabledSchedule: (isEnabledSchedule: boolean) =>
    set({ isEnabledSchedule: isEnabledSchedule }),
  setNote: (note: string) => set({ note: note }),
  setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) =>
    set({ isApplyNoteForAllTask: isApplyNoteForAllTask }),
  setPrice: (price: IPrice | null) => set({ price: price }),
  setCurrency: (currency: { sign: string; code: string }) =>
    set({ currency: currency }),
  setHomeNumber: (homeNumber: string) => set({ homeNumber: homeNumber }),
  setPaymentMethod: (paymentMethod: any) =>
    set({ paymentMethod: paymentMethod }),
  setPromotion: (promotion: any) => set({ promotion: promotion }),
  setLoadingPrice: (loadingPrice: boolean) =>
    set({ loadingPrice: loadingPrice }),
  setLoadingPostTask: (loadingPostTask: boolean) =>
    set({ loadingPostTask: loadingPostTask }),
  setRelatedTask: (relatedTask: IRelatedTask) =>
    set({ relatedTask: relatedTask }),
  setUser: (user: IUser) => set({ user: user }),
  setService: (service: IService) => set({ service: service }),
  reset: () =>
    set({
      address: {},
      duration: 0,
      requirements: [],
      isPremium: false,
      isAutoChooseTasker: true,
      isFavouriteTasker: false,
      gender: '',
      pet: '',
      addons: [],
      date: null,
      schedule: [],
      isEnabledSchedule: false,
      note: '',
      isApplyNoteForAllTask: false,
      dateOptions: [],
      homeNumber: '',
      price: null,
      service: null,
      timezone: 'Asia/Ho_Chi_Minh',
      forceTasker: {},
      isoCode: ISO_CODE.VN,
      dataQuickPostTask: null,
      currency: null,
      paymentMethod: {
        name: 'Cash',
        label: 'PAYMENT_METHOD_DIRECT_CASH',
        value: 'CASH',
        icon: require('../../assets/images/logo-cash.png'),
      },
      promotion: null,
      loadingPrice: false,
      loadingPostTask: false,
      relatedTask: null,
      user: null,
    }),
}));
