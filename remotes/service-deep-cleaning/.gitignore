# OSX
#
.DS_Store
prod.env

# Xcode
#
ios/build
ios/Frameworks/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace

# Android/IJ
#
*.iml
.idea
.gradle
local.properties
android/build
android/app/libs
android/app/build/
android/app/build/intermediates/
android/keystores/debug.keystore

# node.js
#
node_modules/
npm-debug.log
yarn-error.log
yarn.lock
package-lock.json

# BUCK
buck-out/
\.buckd/
# *.keystore

# fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://github.com/fastlane/fastlane/blob/master/fastlane/docs/Gitignore.md

fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots

# Test
tests/build/Logs
tests/build/ModuleCache
tests/build/info.plist
tests/build/Index
tests/build/Build/Intermediates
tests/build/Build/Intermediates.noindex
tests/build/Build/Products/*.m
tests/build/Build/Products/*.h
tests/build/Build/Products/Debug-iphonesimulator/*.app
tests/build/Build/Products/Debug-iphonesimulator/include
tests/build/Build/Products/Debug-iphonesimulator/*.app.dSYM
tests/build/Build/Products/Debug-iphonesimulator/*.framework
tests/build/Build/Products/Debug-iphonesimulator/*.framework.dSYM
tests/build/Build/Products/Debug-iphonesimulator/*.a
!tests/build/Build/Products/Debug-iphonesimulator/libPods-bTaskee.a

tests/build/Build/Products/Release-iphonesimulator/*.app
tests/build/Build/Products/Release-iphonesimulator/include
tests/build/Build/Products/Release-iphonesimulator/*.app.dSYM
tests/build/Build/Products/Release-iphonesimulator/*.framework
tests/build/Build/Products/Release-iphonesimulator/*.framework.dSYM
tests/build/Build/Products/Release-iphonesimulator/*.a
!tests/build/Build/Products/Release-iphonesimulator/libPods-bTaskee.a

#cocoa pod
ios/Pods
Podfile.lock
ios/btaskeePartner.xcworkspace
.xcode.env.local

# fastlane
ios/fastlane/report.xml

#enviroment
prod.env
staging.env

# Google SDK
ios/GoogleSdk
# ios/GoogleService-Info.plist
# android/app/google-services.json

# Zalo Pay
android/zalo-sdk/build/
android/zalo-sdk/zpdk-production-release.aar
android/zalo-sdk/zpdk-sandboxMerchant-release.aar
android/zpdk-release/build/

# react-native-config codegen
ios/tmp.xcconfig

# ci auto gen
result-test
e2e-tempt
artifacts
env-docker


**/.xcode.env.local


# fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://docs.fastlane.tools/best-practices/source-control/

**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Bundle artifact
*.jsbundle

# Ruby / CocoaPods
**/Pods/
/vendor/bundle/

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*

# testing
/coverage

# Yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

*.iml
*.hprof
.cxx/
*.keystore
!debug.keystore

# Xcode
#
build/
ios/Pods
