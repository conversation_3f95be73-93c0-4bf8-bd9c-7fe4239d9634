# Business Design Document - **Deep Cleaning** Service

## **1. Purpose**

Enable users to book a specialized Deep Cleaning service via mobile app, guiding them through address setup, choose task details, scheduling, and payment.

## **2. Scope / Features**

### Choose an Address

* **Property Type Support:** Support for different property categories including houses, apartments, and villas with appropriate service customization
* **Address and Contact Management:** Handle contact details associated with each location including name and phone number for service coordination, allow users to save frequently used addresses and set preferences for future bookings

### Choose Task Details

* **Area & Duration Presets:** A set of mutually-exclusive presets mapping zone size to a default team size and working duration.
* **Adjust-Before-Start Prompt:** After any preset is chosen, an in-context info message appears advising the user they can refine duration with the Tasker before work begins.
* **Scope Reminder:** A persistent warning reminds users that only standard household chores are supported.
* **Detail Drill-In:** A dedicated “Task details” row opens the full task specification page or modal.

### Choose Working Time

* **Date and Time Selection:** Flexible scheduling system allowing users to choose preferred appointment dates and times
* **Notes for Tasker:** Text-based communication system allowing users to provide specific instructions and requests to tasker

### Confirm and Payment

* **Booking Confirmation:** Comprehensive booking summary and confirmation system displaying all service details before final commitment
* **Payment Processing:** Multi-channel payment system supporting various payment methods including digital wallets, cards, and alternative payment options
* **Promotion Management:** Discount and promotion code system allowing users to apply available offers and see immediate savings
* **Contact Updates:** Real-time contact information editing capabilities during the booking confirmation process

## **3. Stakeholders**

* **End Users:** Customers booking child care services
* **Taskers:** Service providers receiving tasks
* **Admin:** Oversee bookings, pricing, and service quality

## **4. Functional Requirements**

### 4.1 Choose an Address

* **Conditional Navigation:** The system checks user's saved addresses upon entering the booking flow:
  * If user has at least one saved address: System automatically proceeds to "Choose Task Details" page, skipping address selection. User can modify address selection at this page if needed.
  * If user has no saved addresses: System directs user to "Choose Location" page for address setup
* **Multi-Input Address System:** Users can input addresses through three distinct methods:
  * GPS location detection for automatic current location identification and nearby address suggestion
  * Manual text entry with auto-complete and address suggestion functionality for precise address specification
  * Interactive map selection with pin-dropping capability for visual location picking and address confirmation
* **Property Type Classification:** Users must specify property type (house/apartment/villa) for each address to ensure appropriate service matching
* **Address Validation and Formatting:** The system validates the address. Users receive feedback if the address is invalid and is prevented to proceeding with booking.
* **Address and Contact Information Management:** Users can store and manage both address details and associated contact information including contact name and phone number with validation, save addresses, edit existing addresses.

### **4.2 Choose Task Details**

* **Area & Duration Selector:** Render six mutually-exclusive cards, each showing an area limit and corresponding team size and duration:

  
  1. **Max 60 m²** – 2 Taskers / 3 hours
  2. **Max 80 m²** – 2 Taskers / 4 hours
  3. **Max 100 m²** – 3 Taskers / 3 hours
  4. **Max 150 m²** – 3 Taskers / 4 hours
  5. **Max 200 m²** – 4 Taskers / 4 hours
  6. **Max 400 m²** – 4 Taskers / 8 hours
* **Before-Start Info Message:**
  * Remains hidden by default.
  * Once **any** area card is tapped, show a green info box:

    > “Before starting the task, you can discuss with the Tasker and adjust the working duration to be suitable with the actual workload.”
* **Scope Warning:**
  * Always visible below the info box (yellow warning):

    > “The service only supports tasks within the scope of household chores. Please consult the task description before scheduling.”
* **Task Details Navigation:**
  * A full-width “Task details” row with an icon and “>” chevron.
  * Tapping opens the **Task Details** page/modal.

### 4.3 Choose Working Time

* **Calendar Date Selection:** Users can interact with an interactive calendar interface with the following specifications:
  * Users see current date clearly highlighted and marked as starting reference point
  * Users can select from next 7 consecutive days displayed as available booking options
  * The system prevents selection of past dates and unavailable future dates (monthly plan)
  * Users see holiday and peak demand period indicators where applicable
* **Time Selection Interface:** Users can utilize an intuitive time picker with comprehensive options:
  * Users see 12-hour format with clear AM/PM designation
  * Users can select 5-minute interval options for flexible scheduling
  * The system enforces minimum 1-hour advance booking requirement when users select current date (tasker start time must be at least 1 hour after booking time)
  * If the user selects time outside of the range 06AM to 11PM, an error popup will be shown
* **Custom Instruction System:** Users can utilize a robust text input system for instructions:
  * Users can leave the optional text field empty without preventing booking completion
  * The system enforces maximum character limit of 400 characters with real-time character counter and remaining count display

### 4.4 Confirm and Payment

* **Complete Booking Summary Display:** Users can review comprehensive booking information:
  * Users see complete address display with property type and contact information
  * Users see detailed service type breakdown
  * Users see scheduled date, time, and recurring schedule details
  * The system displays custom instruction notes if provided
* **Comprehensive Payment Method Support:** Users can select from multiple payment channels with robust processing:
  * Users can choose cash payment option with clear instructions for payment upon task completion
  * Users can use digital wallet integration including bPay, Momo, ZaloPay, ShopeePay,…. with secure connections
  * Users can process credit card payments for Visa and MasterCard with secure 3D authentication
  * Users can access buy-now-pay-later option through Kredivo with installment calculation and approval workflow
  * The system provides payment method validation and error handling for failed transactions
* **Advanced Promotion Code Management:** Users can access comprehensive promotion functionality:
  * Users can enter promotion codes in dedicated text input field with format validation
  * Users see automatic discount calculation and application to total pricing with immediate visual feedback
  * Users can remove promotion codes with pricing recalculation
* **Comprehensive Booking Validation and Confirmation Process:** The system ensures robust booking processing:
  * Multi-step validation ensuring all required fields are completed and properly formatted
  * "Book" button state management enabling only when all requirements are satisfied
  * The system performs final availability and pricing confirmation check before payment processing
  * Secure payment processing according to selected method with comprehensive error handling
  * The system automatically navigates users to Success Booking screen upon successful transaction completion

## **5. Non-Functional Requirements**

* Mobile-first responsive design
* Real-time validation & feedback
* Localization (support Vietnamese & English)
* Secure data handling & storage

## **6. Acceptance Criteria (Testable Steps)**

### **6.1 Choose Location**

* **Choose Location Screen (New Address Entry):**
  * Using GPS to automatically detect their current location.
  * Manually entering full address details.
  * Tapping on the map and confirming with "Pick this location."
* **Location Detail Input & Confirmation:** After selecting or adding a location, users are directed to a screen to:
  * Choose the property type from predefined options: "House/Town house," "Apt/Condo," or "Villa."
  * Input complete address details (”House number”, “Apt/Condo”, or “Villa”)
  * Users can tap "OK" to confirm these address and contact details.

### **6.2 Choose Task Details**

* **Preset Selection**
  * When the user taps any area-duration preset card:
    * That card becomes visually highlighted.
    * Any previously selected card is deselected.
    * The bottom bar’s price and duration update to reflect the chosen preset.
    * The “Next” button becomes enabled (was disabled before first selection).
* **Before-Start Info**
  * Before any selection, the green info box is hidden.
  * Immediately after the first preset is tapped, the green info box appears, advising the user they can adjust duration with the Tasker before starting.
* **Scope Warning**
  * A yellow warning box remains visible at all times, regardless of selection.
* **Task Details Drill-In**
  * Tapping the “Task details” row opens the full Task Details screen or modal.
  * Users can view and dismiss that screen/modal to return to Choose Task Details.

### **6.3 Choose Working Time**

* Displays **7 consecutive days starting from the current date**, allowing users to select a preferred booking date. When a user selects a date, **the selected date is highlighted** to indicate it is chosen.
* The "Time" picker allows users to select hours and minutes with AM/PM. If the user selects time outside of the range 06AM to 11PM, an error popup will be shown .
* The total price and duration displayed at the bottom updates dynamically with date and time selections.
* The "Notes for Tasker" text area allows free-form text input. It should allow being empty. The user can enter 400 characters, no additional input is accepted.

### **6.4 Confirm & Payment**

* The booking summary displays all essential details, including:
  * Location of service.
  * Client contact information with "Change" button.
  * Selected date, time, and estimated duration of the task.
  * Description of the task.
  * Payment method options: Cash option with icon, Promotion option with icon.
* **Payment Method Selection:**
  * **Supported Methods**: The system supports the following payment methods:
    * Cash
    * Electronic payments (bPay, Momo, ZaloPay, ShopeePay, VietQR, VNPAY, Visa/Master, Pay later/Installment via Kredivo)
  * **Selection Flow**:
    * When user opens the Payment Method screen, all available methods are displayed.
    * Once a method is selected, the system stores the selection and navigates the user back to the **Confirm & Pay** screen.
    * If Visa/Master is selected and no card is saved, the system navigates to the **Add Card** screen for user input.
* **Promotion Code Handling**:
  * User may enter a promotion code or select from available vouchers.
  * If a valid promotion code is applied, the system:
    * Updates the discount
    * Recalculates the total price in the booking summary
    * Displays the promotion code and corresponding discount value
  * If the promotion code is invalid:
    * A notification is shown: **“The promotion code is invalid. Please check again.”**
    * No price change is applied
* Final total displayed prominently.
* "Book" button available for final confirmation only when all required information is complete.
* Upon tapping **“Book”**, system processes the booking and navigates the user to a **Booking Confirmation screen** upon success.

### **6.5 Error & Edge Cases**

* Past date/time selection shows appropriate error message.
* Incomplete address fields prevent progression to the next step.
* Invalid or unsupported address shows validation error.
* Invalid contact information displays validation errors.
* Network errors during booking show retry options.
* Payment method selection required before final booking.

## **7. Open Questions / Assumptions**

* What users can edit in their bookings after confirmation? What will happen if their edit makes the total price lower?
* What is the process if a user cancels before scheduled time?
* Should the user only be able to book tasks within the current week?
* Max number of promo codes per booking?
* Is pre‑inspection mandatory for large areas?
* Price and time increments for each add-on are clearly defined in the business logic.
* Fees for manual tasker selection and rules are transparent and consistently enforced.

## **8. Visual Reference**

*Screens and UI flows as shown in the provided images (06/2025 booking flow, options, summary, confirmation, etc.)*


---

**End of Document**

