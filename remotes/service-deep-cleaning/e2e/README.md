# Deep Cleaning Service - E2E Test Suite

## 📋 Overview

This comprehensive E2E test suite validates the complete deep cleaning booking flow using Detox framework, following the user's specific requirements for testID-based element selection, scroll-to-reveal patterns, and performance optimization.

## 🎯 Key Features

### ✅ Mandatory Requirements Implemented

- **TestID-Only Element Selection**: Uses ONLY testID-based selectors (never text-based selectors)
- **Sequential Flow Validation**: Address Selection → Duration Selection → DateTime & Notes → Booking Confirmation
- **Scroll-to-Reveal Patterns**: Incremental scrolling (~150 pixels) to prevent overshooting elements
- **Performance Target**: Optimized for 3-5 minute execution time
- **State Management**: Proper resetData initialization and Zustand store integration

### 🧪 Test Coverage

1. **Sequential Booking Flow** (`DeepCleaningSequentialFlow.e2e.js`)
   - Complete end-to-end booking validation
   - UI component visibility testing
   - Scroll-to-reveal pattern validation

2. **Data Verification** (`DeepCleaningDataVerification.e2e.js`)
   - Price calculation accuracy
   - Service configuration validation
   - Promotion code application
   - Data persistence across navigation

3. **Error Handling** (`DeepCleaningErrorHandling.e2e.js`)
   - Form validation errors
   - Network connectivity issues
   - Invalid input handling
   - Edge case scenarios

## 🚀 Quick Start

### Prerequisites

```bash
# Ensure Detox is installed and configured
yarn install
yarn detox build --configuration ios.sim.debug
```

### Running Tests

```bash
# Run all deep cleaning E2E tests
yarn detox test --configuration ios.sim.debug remotes/service-deep-cleaning/e2e/

# Run specific test suite
yarn detox test --configuration ios.sim.debug remotes/service-deep-cleaning/e2e/DeepCleaningSequentialFlow.e2e.js

# Run with verbose logging
yarn detox test --configuration ios.sim.debug --loglevel verbose remotes/service-deep-cleaning/e2e/

# Run with performance monitoring
yarn detox test --configuration ios.sim.debug --record-logs all remotes/service-deep-cleaning/e2e/
```

## 📱 TestID Reference

### Address Selection Screen
```javascript
testID="scrollChooseAddress"    // Address list container
testID="address1"               // First address option
testID="address2"               // Second address option
```

### Duration Selection Screen
```javascript
testID="area60"                 // 60m² option (2 taskers, 3h)
testID="area80"                 // 80m² option (2 taskers, 4h)
testID="area100"                // 100m² option (3 taskers, 3h)
testID="area150"                // 150m² option (3 taskers, 4h)
testID="area200"                // 200m² option (4 taskers, 4h)
testID="area400"                // 400m² option (4 taskers, 8h)
testID="tipsDeepCleaning"       // Tips information box
testID="warningDeepCleaning"    // Warning message box
testID="btnNextStep2"           // Navigation to next step
```

### Date/Time Selection Screen
```javascript
testID="scrollChooseDateTime"   // Main screen container
testID="taskNote"               // Notes input field
testID="btnNextStep3"           // Navigation to confirmation
```

### Confirmation Screen
```javascript
testID="scrollViewStep4"        // Main screen container
testID="taskDetail"             // Task details section
testID="area"                   // Area display text
testID="numberOfTasker"         // Number of taskers display
testID="choosePaymentMethod"    // Payment method selector
testID="price"                  // Final price display
testID="originPrice"            // Original price (with promotion)
testID="btnSubmitPostTask"      // Final booking button
```

### Success Screen
```javascript
testID="postTaskSuccessBtn"     // Success confirmation button
```

## 🔧 Helper Functions

### Core Helpers (`DeepCleaningHelpers.js`)

```javascript
// Scroll-to-reveal with incremental scrolling
await scrollToRevealElement('containerId', 'targetElementId', 'down', 10, 150);

// Sequential flow validation
await validateSequentialFlow({
  address: 'address1',
  area: '80',
  paymentMethod: 'choosePaymentMethod'
});

// Performance monitoring
await monitorPerformance('Test Name', async () => {
  // Test implementation
});
```

### Step-Specific Validators

```javascript
// Individual step validation
await validateAddressSelection('address1');
await validateDurationSelection('80');
await validateDateTimeSelection();
await validateBookingConfirmation('choosePaymentMethod');

// Data accuracy validation
await validateBookingData({
  area: '80',
  numberOfTasker: '2',
  expectedPrice: '640,000 VND'
});
```

## ⚡ Performance Optimization

### Execution Time Targets

- **Individual Test**: < 2 minutes
- **Complete Suite**: 3-5 minutes
- **Critical Path Tests**: < 1 minute

### Optimization Strategies

1. **Efficient Element Selection**
   ```javascript
   // ✅ Optimized approach
   await waitForElement('elementId', WAIT_TIMES.SHORT);
   await tapId('elementId');
   
   // ❌ Avoid long waits
   await waitForElement('elementId', 10000);
   ```

2. **Batch Operations**
   ```javascript
   // ✅ Batch multiple interactions
   await batchInteractions([
     { type: 'tap', elementId: 'address1' },
     { type: 'verify', elementId: 'btnNextStep2' },
     { type: 'tap', elementId: 'btnNextStep2' }
   ]);
   ```

3. **Smart Scrolling**
   ```javascript
   // ✅ Incremental scrolling (150px)
   await scrollToRevealElement('container', 'element', 'down', 10, 150);
   
   // ❌ Aggressive scrolling (can overshoot)
   await swipe('container', 'up', 'fast');
   ```

## 🐛 Troubleshooting

### Common Issues

#### Element Not Found
```bash
Error: Cannot find element with testID="elementId"
```

**Solutions:**
- Verify testID exists in component JSX
- Check if element requires scrolling to be visible
- Increase wait timeout for slow loading
- Use scroll-to-reveal helper

#### Scroll Issues
```bash
Error: Element exists but not accessible
```

**Solutions:**
- Use `scrollToRevealElement` helper
- Reduce scroll increment (100px instead of 150px)
- Increase max scroll attempts
- Verify scroll container testID

#### Performance Issues
```bash
Test execution exceeds 5-minute target
```

**Solutions:**
- Use shorter wait times for quick operations
- Implement batch operations
- Optimize element selection strategy
- Use performance monitoring to identify bottlenecks

### Debug Commands

```bash
# Maximum logging
detox test --loglevel trace remotes/service-deep-cleaning/e2e/

# Record everything
detox test --record-logs all --take-screenshots all --record-videos all remotes/service-deep-cleaning/e2e/

# Run specific test with debugging
detox test --loglevel verbose remotes/service-deep-cleaning/e2e/DeepCleaningSequentialFlow.e2e.js
```

## 📊 Test Maintenance

### Regular Maintenance Tasks

1. **TestID Validation**
   - Verify all testIDs exist in components
   - Check for testID naming consistency
   - Update testIDs when components change

2. **Performance Monitoring**
   - Track test execution times
   - Identify and optimize slow tests
   - Monitor for performance regressions

3. **Data Updates**
   - Update test data for new area configurations
   - Refresh promotion codes and pricing
   - Validate currency and formatting changes

### Best Practices

1. **Test Structure**
   - Keep tests focused and atomic
   - Use descriptive test names
   - Implement proper setup/teardown

2. **Element Selection**
   - Always use testID-based selectors
   - Implement scroll-to-reveal for off-screen elements
   - Use consistent naming conventions

3. **Error Handling**
   - Include comprehensive error scenarios
   - Test edge cases and validation
   - Implement graceful failure handling

## 📈 Metrics and Reporting

### Performance Metrics
- Test execution time tracking
- Element interaction timing
- Scroll operation efficiency
- Memory usage monitoring

### Coverage Metrics
- TestID coverage validation
- Flow completion rates
- Error scenario coverage
- Business rule validation

## 🔄 Continuous Integration

### CI/CD Integration
```yaml
# Example CI configuration
test:
  script:
    - yarn detox build --configuration ios.sim.debug
    - yarn detox test --configuration ios.sim.debug --cleanup
  artifacts:
    reports:
      junit: detox-results.xml
    paths:
      - screenshots/
      - videos/
```

### Quality Gates
- All tests must pass
- Execution time < 5 minutes
- No testID-based element selection failures
- Performance regression checks

## 🔧 Configuration Files

### Detox Configuration (`.detoxrc.js`)
```javascript
module.exports = {
  testRunner: 'jest',
  runnerConfig: 'e2e/jest.config.js',
  configurations: {
    'ios.sim.debug': {
      device: 'simulator',
      app: 'ios/build/Build/Products/Debug-iphonesimulator/deepCleaning.app'
    }
  }
};
```

### Jest Configuration (`e2e/jest.config.js`)
```javascript
module.exports = {
  rootDir: '..',
  testMatch: ['<rootDir>/e2e/**/*.e2e.js'],
  testTimeout: 120000,
  maxWorkers: 1,
  globalSetup: 'detox/runners/jest/globalSetup',
  globalTeardown: 'detox/runners/jest/globalTeardown',
  reporters: ['detox/runners/jest/reporter'],
  testEnvironment: 'detox/runners/jest/testEnvironment',
  verbose: true,
  setupFilesAfterEnv: ['<rootDir>/e2e/setup.ts'],
};
```

### Test Setup (`e2e/setup.ts`)
```typescript
import { device } from 'detox';

beforeAll(async () => {
  await device.launchApp({
    permissions: {
      notifications: 'YES',
      location: 'always',
      camera: 'YES',
      photos: 'YES',
    },
    launchArgs: {
      isE2ETesting: true,
      initialRouteName: 'DeepCleaningService',
      detoxDebugSynchronization: 1000,
      isoCode: 'VN',
    },
  });
}, 300000);

afterAll(async () => {
  await device.terminateApp();
});
```

---

**📝 Note**: This test suite follows the established patterns from other services in the super-app-workspace and implements all user-specified requirements for comprehensive deep cleaning service E2E testing.
