/**
 * Deep Cleaning E2E Test Helper Functions
 *
 * This module provides reusable helper functions for deep cleaning service E2E tests
 * following the established patterns from other services in the super-app-workspace.
 *
 * Key Features:
 * - Scroll-to-reveal patterns with incremental scrolling (~150 pixels)
 * - TestID-based element selection (mandatory)
 * - Sequential flow validation
 * - Performance optimization for 3-5 minute execution times
 * - State management with Zustand stores
 */

const {
  initData,
  tapId,
  tapText,
  waitForElement,
  expectElementVisible,
  expectIdToHaveText,
  swipe,
  scroll,
  typeToTextField,
  expectElementNotExist,
  expectElementNotVisible,
  checkElementVisible,
} = require('../../step-definition');

// Wait time constants for performance optimization
const WAIT_TIMES = {
  SHORT: 1000,    // Quick element checks
  NORMAL: 2000,   // Standard element waiting
  LONG: 5000,     // Complex operations
  SCROLL: 200,    // Scroll settling time
};

// Scroll configuration for precise element access
const SCROLL_CONFIG = {
  INCREMENT: 150,     // Pixels per scroll (prevents overshooting)
  MAX_ATTEMPTS: 10,   // Maximum scroll attempts
  DIRECTION: {
    UP: 'up',
    DOWN: 'down',
  },
};

/**
 * Enhanced scroll-to-reveal element helper with incremental scrolling
 * Implements the user's preferred incremental scrolling pattern (~150 pixels)
 * to prevent overshooting and making interactive elements inaccessible
 *
 * @param {string} containerId - ScrollView testID
 * @param {string} targetElementId - Element to find and make visible
 * @param {string} direction - Scroll direction ('up' or 'down')
 * @param {number} maxAttempts - Maximum scroll attempts
 * @param {number} scrollIncrement - Pixels per scroll
 * @returns {Promise<boolean>} - True if element found, false otherwise
 */
const scrollToRevealElement = async (
  containerId,
  targetElementId,
  direction = SCROLL_CONFIG.DIRECTION.DOWN,
  maxAttempts = SCROLL_CONFIG.MAX_ATTEMPTS,
  scrollIncrement = SCROLL_CONFIG.INCREMENT,
) => {
  let attempts = 0;

  while (attempts < maxAttempts) {
    try {
      // Check if element is visible
      await waitForElement(targetElementId, WAIT_TIMES.SHORT);
      await expectElementVisible(targetElementId);
      console.log(`✅ Element ${targetElementId} found after ${attempts} scroll attempts`);
      return true;
    } catch (error) {
      // Element not visible, scroll to reveal it
      console.log(`🔍 Scrolling to reveal ${targetElementId}, attempt ${attempts + 1}`);
      await scrollPrecise(containerId, direction, scrollIncrement);
      attempts++;
    }
  }

  console.error(`❌ Element ${targetElementId} not found after ${maxAttempts} scroll attempts`);
  return false;
};

/**
 * Precise incremental scrolling helper to avoid overshooting elements
 * Uses controlled pixel-based scrolling instead of aggressive swipes
 *
 * @param {string} containerId - ScrollView testID
 * @param {string} direction - Scroll direction
 * @param {number} pixels - Pixels to scroll
 */
const scrollPrecise = async (containerId, direction = SCROLL_CONFIG.DIRECTION.DOWN, pixels = SCROLL_CONFIG.INCREMENT) => {
  // Use precise pixel-based scrolling instead of aggressive swipe
  // Default 150 pixels provides controlled movement without overshooting
  await scroll(containerId, pixels, direction, 0.5, 0.5);
  // Small delay to allow UI to settle
  await new Promise((resolve) => setTimeout(resolve, WAIT_TIMES.SCROLL));
};

/**
 * Validates the sequential deep cleaning booking flow
 * Ensures proper navigation through: Address → Duration → DateTime → Confirmation
 *
 * @param {Object} flowData - Data for flow validation
 * @param {string} flowData.address - Address selection
 * @param {string} flowData.area - Area selection (60, 80, 100, 150, 200, 400)
 * @param {string} flowData.paymentMethod - Payment method
 */
const validateSequentialFlow = async (flowData) => {
  const { address, area, paymentMethod } = flowData;

  console.log('🔄 Starting sequential deep cleaning booking flow validation');

  // Step 1: Address Selection
  await validateAddressSelection(address);

  // Step 2: Duration Selection
  await validateDurationSelection(area);

  // Step 3: DateTime & Notes Selection
  await validateDateTimeSelection();

  // Step 4: Booking Confirmation
  await validateBookingConfirmation(paymentMethod);

  console.log('✅ Sequential flow validation completed successfully');
};

/**
 * Validates address selection step
 * Ensures proper address selection and navigation to duration step
 *
 * @param {string} addressKey - Address identifier (e.g., 'address1', 'address2')
 */
const validateAddressSelection = async (addressKey = 'address1') => {
  console.log('📍 Validating address selection step');

  // Wait for address list to load
  await waitForElement('scrollChooseAddress', WAIT_TIMES.LONG);
  await expectElementVisible('scrollChooseAddress');

  // Select address
  await waitForElement(addressKey, WAIT_TIMES.NORMAL);
  await expectElementVisible(addressKey);
  await tapId(addressKey);

  // Verify navigation to duration selection
  await waitForElement('btnNextStep2', WAIT_TIMES.LONG);

  console.log('✅ Address selection validated');
};

/**
 * Validates duration selection step
 * Tests area selection and verifies price updates
 *
 * @param {string} area - Area size (60, 80, 100, 150, 200, 400)
 */
const validateDurationSelection = async (area = '80') => {
  console.log(`⏱️ Validating duration selection step for area ${area}m²`);

  // Wait for duration options to load
  await waitForElement(`area${area}`, WAIT_TIMES.NORMAL);

  // Select area/duration option
  await expectElementVisible(`area${area}`);
  await tapId(`area${area}`);

  // Verify tips appear after selection
  await waitForElement('tipsDeepCleaning', WAIT_TIMES.NORMAL);
  await expectElementVisible('tipsDeepCleaning');

  // Verify warning text is visible
  await expectElementVisible('warningDeepCleaning');

  // Verify next button is enabled
  await waitForElement('btnNextStep2', WAIT_TIMES.NORMAL);
  await expectElementVisible('btnNextStep2');

  // Navigate to next step
  await tapId('btnNextStep2');

  console.log('✅ Duration selection validated');
};

/**
 * Validates date/time selection step
 * Tests date picker, time picker, and notes functionality
 */
const validateDateTimeSelection = async () => {
  console.log('📅 Validating date/time selection step');

  // Wait for date/time screen to load
  await waitForElement('scrollChooseDateTime', WAIT_TIMES.LONG);
  await expectElementVisible('scrollChooseDateTime');

  // Verify next button is available
  await scrollToRevealElement('scrollChooseDateTime', 'btnNextStep3');
  await expectElementVisible('btnNextStep3');

  // Navigate to confirmation
  await tapId('btnNextStep3');

  console.log('✅ Date/time selection validated');
};

/**
 * Validates booking confirmation step
 * Tests task details display, payment method selection, and final booking
 *
 * @param {string} paymentMethod - Payment method testID
 */
const validateBookingConfirmation = async (paymentMethod = 'choosePaymentMethod') => {
  console.log('💳 Validating booking confirmation step');

  // Wait for confirmation screen to load
  await waitForElement('scrollViewStep4', WAIT_TIMES.LONG);
  await expectElementVisible('scrollViewStep4');

  // Verify task details are displayed
  await expectElementVisible('taskDetail');

  // Scroll to reveal payment method if needed
  await scrollToRevealElement('scrollViewStep4', paymentMethod);

  // Verify payment method selection
  await expectElementVisible(paymentMethod);

  // Scroll to reveal booking button
  await scrollToRevealElement('scrollViewStep4', 'btnSubmitPostTask');
  await expectElementVisible('btnSubmitPostTask');

  console.log('✅ Booking confirmation validated');
};

/**
 * Validates data accuracy throughout the booking flow
 * Checks price calculations, service configuration, and booking details
 *
 * @param {Object} expectedData - Expected booking data
 */
const validateBookingData = async (expectedData) => {
  const { area, numberOfTasker, expectedPrice } = expectedData;

  console.log('📊 Validating booking data accuracy');

  // Verify area display
  if (area) {
    await expectIdToHaveText('area', `Tối đa ${area}m²`);
  }

  // Verify number of taskers
  if (numberOfTasker) {
    await expectIdToHaveText('numberOfTasker', `${numberOfTasker} người`);
  }

  // Verify price if provided
  if (expectedPrice) {
    await expectIdToHaveText('price', expectedPrice);
  }

  console.log('✅ Booking data validation completed');
};

/**
 * Resets application state for clean test execution
 * Implements proper state management using Zustand stores
 */
const resetAppState = async () => {
  console.log('🔄 Resetting application state');

  // Reset data using the established pattern
  await initData('resetData');

  console.log('✅ Application state reset completed');
};

/**
 * Handles successful booking completion
 * Validates success screen and navigation options
 */
const validateBookingSuccess = async () => {
  console.log('🎉 Validating booking success');

  // Wait for success screen
  await waitForElement('postTaskSuccessBtn', WAIT_TIMES.LONG);
  await expectElementVisible('postTaskSuccessBtn');

  // Tap success button to follow task
  await tapId('postTaskSuccessBtn');

  console.log('✅ Booking success validated');
};

/**
 * Validates error states and edge cases
 * Tests validation scenarios and error handling
 *
 * @param {Object} errorScenario - Error scenario configuration
 */
const validateErrorHandling = async (errorScenario) => {
  const { type, expectedMessage, elementId } = errorScenario;

  console.log(`⚠️ Validating error handling for: ${type}`);

  switch (type) {
    case 'INVALID_TIME':
      // Test invalid time selection
      await validateInvalidTimeError();
      break;
    case 'PAYMENT_FAILURE':
      // Test payment method validation
      await validatePaymentError(elementId);
      break;
    case 'NETWORK_ERROR':
      // Test network connectivity issues
      await validateNetworkError();
      break;
    default:
      console.log(`Unknown error type: ${type}`);
  }

  console.log('✅ Error handling validation completed');
};

/**
 * Validates invalid time selection error
 */
const validateInvalidTimeError = async () => {
  // Implementation for time validation errors
  console.log('⏰ Testing invalid time selection');
  // Add specific time validation logic here
};

/**
 * Validates payment method errors
 *
 * @param {string} elementId - Payment element to test
 */
const validatePaymentError = async (elementId) => {
  console.log('💳 Testing payment method validation');

  // Try to proceed without selecting payment method
  if (elementId) {
    const exists = await checkElementVisible(elementId, WAIT_TIMES.SHORT);
    if (!exists) {
      console.log('Payment method not selected - validation working');
    }
  }
};

/**
 * Validates network connectivity error handling
 */
const validateNetworkError = async () => {
  console.log('🌐 Testing network error handling');
  // Implementation for network error scenarios
};

/**
 * Performance monitoring helper
 * Tracks test execution time and identifies bottlenecks
 *
 * @param {string} testName - Name of the test being monitored
 * @param {Function} testFunction - Test function to execute
 * @returns {Promise<Object>} - Test results with timing information
 */
const monitorPerformance = async (testName, testFunction) => {
  const startTime = Date.now();
  console.log(`⏱️ Starting performance monitoring for: ${testName}`);

  try {
    const result = await testFunction();
    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`✅ ${testName} completed in ${duration}ms`);

    // Log warning if test takes longer than target (5 minutes = 300,000ms)
    if (duration > 300000) {
      console.warn(`⚠️ ${testName} exceeded 5-minute target: ${duration}ms`);
    }

    return {
      success: true,
      duration,
      result,
    };
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;

    console.error(`❌ ${testName} failed after ${duration}ms:`, error.message);

    return {
      success: false,
      duration,
      error: error.message,
    };
  }
};

/**
 * Batch element interaction helper
 * Performs multiple element interactions efficiently
 *
 * @param {Array} interactions - Array of interaction objects
 */
const batchInteractions = async (interactions) => {
  console.log(`🔄 Executing ${interactions.length} batch interactions`);

  for (const interaction of interactions) {
    const { type, elementId, value, waitTime = WAIT_TIMES.NORMAL } = interaction;

    try {
      await waitForElement(elementId, waitTime);

      switch (type) {
        case 'tap':
          await tapId(elementId);
          break;
        case 'type':
          await typeToTextField(elementId, value);
          break;
        case 'verify':
          await expectElementVisible(elementId);
          break;
        case 'verifyText':
          await expectIdToHaveText(elementId, value);
          break;
        default:
          console.warn(`Unknown interaction type: ${type}`);
      }

      console.log(`✅ ${type} interaction completed for ${elementId}`);
    } catch (error) {
      console.error(`❌ ${type} interaction failed for ${elementId}:`, error.message);
      throw error;
    }
  }

  console.log('✅ Batch interactions completed');
};

module.exports = {
  // Core helper functions
  scrollToRevealElement,
  scrollPrecise,
  validateSequentialFlow,

  // Step-specific validators
  validateAddressSelection,
  validateDurationSelection,
  validateDateTimeSelection,
  validateBookingConfirmation,
  validateBookingData,
  validateBookingSuccess,

  // Error handling and validation
  validateErrorHandling,
  validateInvalidTimeError,
  validatePaymentError,
  validateNetworkError,

  // Performance and optimization
  monitorPerformance,
  batchInteractions,

  // Utility functions
  resetAppState,

  // Constants
  WAIT_TIMES,
  SCROLL_CONFIG,
};
