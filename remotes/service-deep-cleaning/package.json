{"name": "deepCleaning", "version": "0.0.1", "private": true, "scripts": {"start": "react-native start --port 9006 --reset-cache", "test": "jest", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\"", "typecheck": "tsc", "bundle": "pnpm bundle:ios && pnpm bundle:android", "bundle:ios": "react-native bundle --platform ios --entry-file index.js --dev false", "bundle:android": "react-native bundle --platform android --entry-file index.js --dev false", "build": "yarn bundle:ios && yarn bundle:android && mkdir -p ../../deploy/firebase/deep-cleaning && cp -r dist/* ../../deploy/firebase/deep-cleaning/", "align-deps": "rnx-align-deps --write", "check-deps": "rnx-align-deps", "reset": "rm -rf $TMPDIR/react-* && watchman watch-del-all && rm -rf node_modules && rm -rf ios/build && rm -rf ios/Pods && pnpm install && pnpm pod-install && cd android && rm -rf app/build && cd ../ && npx patch-package && echo '✅ Reset done'", "pod-install": "cd ios && RCT_NEW_ARCH_ENABLED=1 pod install", "e2e:build": "detox build", "e2e:test": "detox test", "e2e:build:ios": "detox build -c ios", "e2e:test:ios": "detox test -c ios", "e2e:build:android": "detox build -c android", "e2e:test:android": "detox test -c android", "e2e:clean": "detox clean-framework-cache", "e2e:rebuild": "detox build-framework-cache", "e2e:test:verbose": "detox test --loglevel trace", "e2e:test:debug": "detox test --record-logs all --take-screenshots all", "e2e:ci": "detox build && detox test --cleanup", "e2e:standalone": "detox test e2e/CleaningServiceBooking.e2e.js", "e2e:verify": "node e2e/verify-setup.js"}, "dependencies": {"@btaskee/auth-store": "0.0.1", "@btaskee/design-system": "0.0.11", "@module-federation/enhanced": "0.11.3", "@react-navigation/native": "7.0.14", "@react-navigation/native-stack": "7.2.0", "@rneui/base": "4.0.0-rc.8", "@rneui/themed": "4.0.0-rc.8", "axios": "1.8.4", "buffer": "6.0.3", "dayjs": "1.9.6", "i18next": "25.1.3", "lodash": "4.17.16", "lodash-es": "4.17.21", "lottie-react-native": "7.2.2", "react": "18.3.1", "react-i18next": "15.5.1", "react-native": "0.77.2", "react-native-gesture-handler": "2.25.0", "react-native-markdown-display": "7.0.2", "react-native-reanimated": "3.17.5", "react-native-safe-area-context": "5.4.0", "zustand": "5.0.3"}, "devDependencies": {"@babel/core": "7.25.2", "@babel/plugin-syntax-typescript": "7.25.9", "@babel/preset-env": "7.25.3", "@babel/runtime": "7.25.0", "@btaskee/config": "0.1.28", "@btaskee/sdk": "0.0.7", "@callstack/repack": "5.1.0", "@callstack/eslint-config": "15.0.0", "@callstack/repack-plugin-reanimated": "5.1.1", "@react-native/babel-plugin-codegen": "0.76.5", "@commitlint/cli": "19.8.1", "@commitlint/config-conventional": "19.8.1", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.77.2", "@react-native/codegen": "0.79.2", "@react-native/eslint-config": "0.77.2", "@react-native/eslint-plugin": "0.77.2", "@react-native/metro-config": "0.77.2", "@react-native/typescript-config": "0.77.2", "@rnx-kit/align-deps": "2.5.1", "@rspack/core": "1.2.8", "@swc/helpers": "0.5.15", "@types/detox": "18.1.3", "@types/jest": "29.5.13", "@types/lodash": "^4.17.17", "@types/lodash-es": "4.17.12", "@types/react": "18.2.6", "@types/react-native-vector-icons": "6.4.18", "@types/react-test-renderer": "18.0.0", "@typescript-eslint/eslint-plugin": "7.1.1", "@typescript-eslint/parser": "7.1.1", "babel-jest": "29.6.3", "babel-plugin-transform-inline-environment-variables": "0.4.4", "detox": "^20.20.1", "eslint": "8.43.0", "eslint-config-prettier": "8.5.0", "eslint-plugin-detox": "1.0.0", "eslint-plugin-eslint-comments": "3.2.0", "eslint-plugin-extra-rules": "0.0.0-development", "eslint-plugin-ft-flow": "2.0.1", "eslint-plugin-import": "2.31.0", "eslint-plugin-jest": "27.9.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-react": "7.30.1", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-react-native": "4.0.0", "eslint-plugin-simple-import-sort": "12.1.1", "husky": "8.0.3", "jest": "29.6.3", "patch-package": "8.0.0", "postinstall-postinstall": "2.1.0", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4", "@rsdoctor/rspack-plugin": "0.4.13", "babel-plugin-syntax-hermes-parser": "0.25.1"}, "engines": {"node": ">=18"}, "rnx-kit": {"kitType": "app", "alignDeps": {"presets": ["./node_modules/@btaskee/sdk/preset"], "requirements": ["@btaskee/sdk@0.0.7"], "capabilities": ["super-app"]}}}