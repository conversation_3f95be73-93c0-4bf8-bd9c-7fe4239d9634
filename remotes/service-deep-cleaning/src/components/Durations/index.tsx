import React, { useMemo } from 'react';
import {
  BlockView,
  Card,
  CText,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { IDuration, IServiceDeepCleaning } from '@types';

import { useI18n } from '@hooks';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

interface DurationProps {
  onChange: (duration: IDuration) => void;
  numberOfTasker: number;
}

export const Duration = ({ onChange, numberOfTasker }: DurationProps) => {
  const { t } = useI18n();
  const { service, address, duration, setDuration } = usePostTaskStore();
  const { detailAreaV3 } = service as IServiceDeepCleaning;

  const durationList = useMemo(
    () => detailAreaV3?.find?.((e) => e?.name === address?.homeType)?.value,
    [detailAreaV3, address?.homeType],
  );

  const handleClick = (newDuration: IDuration) => async () => {
    setDuration(newDuration.duration);
    onChange && onChange(newDuration);
  };

  const checkActive = useMemo(
    () => (newDuration: IDuration) => {
      return Boolean(
        duration === newDuration.duration &&
          numberOfTasker === newDuration.numberOfTaskers,
      );
    },
    [duration, numberOfTasker],
  );

  if (!detailAreaV3 || !durationList) {
    return null;
  }

  return (
    <BlockView
      row
      wrap
      jBetween
      margin={{ bottom: Spacing.SPACE_16 }}
    >
      {durationList.map((dur, index) => {
        const isActive = checkActive(dur);
        return (
          <TouchableOpacity
            testID={`area${dur?.area}`}
            activeOpacity={0.6}
            key={index}
            style={styles.itemBtn}
            onPress={handleClick(dur)}
          >
            <Card
              row
              center
              style={[styles.wrapButton, isActive ? styles.borderActive : {}]}
            >
              <BlockView style={styles.leftContent}>
                <CText
                  bold
                  style={[
                    styles.txtDuration,
                    isActive ? styles.textActive : {},
                  ]}
                >
                  {t('AREA_ITEM', { t: dur?.area })}
                </CText>
                <CText style={styles.txtArea}>
                  {t('AREA_ITEM_DEEP_CLEANING', {
                    t1: dur.numberOfTaskers,
                    t2: dur.duration,
                  })}
                </CText>
              </BlockView>
              <BlockView />
            </Card>
          </TouchableOpacity>
        );
      })}
    </BlockView>
  );
};
