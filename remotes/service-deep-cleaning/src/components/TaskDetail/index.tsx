import React from 'react';
import {
  BlockView,
  Card,
  ColorsV2,
  ConditionView,
  CText,
  DateTimeHelpers,
  Spacing,
  WorkingTime,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';
import { usePostTaskStore } from '@stores';

import styles from './styles';

const TaskDetail = () => {
  const { t } = useI18n();

  const { note, date, duration, address, area, numberOfTasker } =
    usePostTaskStore();
  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

  const shouldRenderNote = React.useMemo(() => {
    if (!note) {
      return null;
    }
    return (
      <BlockView
        row
        margin={{ top: Spacing.SPACE_08 }}
        padding={{ top: Spacing.SPACE_16 }}
        border={{ top: { width: 1, color: ColorsV2.neutral100 } }}
      >
        <CText style={styles.txtVLabel}>{t('LABEL_NOTE_FOR_TASKER')}</CText>
        <CText
          testID={'taskNote'}
          style={styles.txtValue}
        >
          {note}
        </CText>
      </BlockView>
    );
  }, [note, t]);

  return (
    <BlockView>
      <BlockView style={styles.panel}>
        <CText
          bold
          style={styles.txtPanel}
        >
          {t('TASK_INFO')}
        </CText>
      </BlockView>
      <Card style={{ marginTop: Spacing.SPACE_16 }}>
        <WorkingTime
          date={date}
          duration={duration}
          timezone={timezone}
        />
        <BlockView style={styles.wrapDetail}>
          <CText
            bold
            style={styles.subPanel}
          >
            {t('TASK_DETAIL')}
          </CText>

          <ConditionView
            condition={Boolean(area)}
            viewTrue={
              <BlockView style={styles.wrapItemDetail}>
                <CText style={styles.txt_dataItem}>{t('WORKLOAD')}</CText>
                <CText style={styles.txt_value}>
                  {t('AREA_ITEM', { t: area })}
                </CText>
              </BlockView>
            }
          />
          <ConditionView
            condition={Boolean(numberOfTasker)}
            viewTrue={
              <BlockView style={styles.wrapItemDetail}>
                <CText style={styles.txt_dataItem}>
                  {t('NUMBER_OF_TASKERS')}
                </CText>
                <CText style={styles.txt_value}>
                  {t('NUMBER_OF_PEOPLE', {
                    t: numberOfTasker,
                  })}
                </CText>
              </BlockView>
            }
          />
          {shouldRenderNote}
        </BlockView>
      </Card>
    </BlockView>
  );
};

export default TaskDetail;
