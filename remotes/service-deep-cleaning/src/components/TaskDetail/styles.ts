import { StyleSheet } from 'react-native';
import { ColorsV2, FontSizes, Spacing } from '@btaskee/design-system';

export default StyleSheet.create({
  panel: {
    marginTop: Spacing.SPACE_32,
    marginBottom: Spacing.SPACE_16,
    justifyContent: 'space-between',
  },
  txtPanel: {
    fontSize: FontSizes.SIZE_16,
  },
  subPanel: {
    marginBottom: 10,
    color: ColorsV2.neutral800,
  },
  txtVLabel: {
    width: '35%',
    paddingRight: 10,
    color: ColorsV2.neutral400,
  },
  txtValue: {
    width: '65%',
    textAlign: 'right',
  },
  group: {
    flexDirection: 'row',
    paddingVertical: 5,
    // alignItems: 'center',
  },
  wrapDetail: {
    marginTop: Spacing.SPACE_08,
  },
  wrapItemDetail: {
    marginBottom: Spacing.SPACE_12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  txt_dataItem: {
    width: '35%',
    paddingRight: 10,
    color: ColorsV2.neutral400,
  },
  txt_value: {
    width: '65%',
    textAlign: 'right',
  },
});
