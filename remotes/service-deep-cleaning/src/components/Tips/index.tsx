import React, { useEffect, useRef } from 'react';
import { Animated } from 'react-native';
import {
  AnimationHelpers,
  BlockView,
  BorderRadius,
  ColorsV2,
  CText,
  IconAssets,
  IconImage,
  Spacing,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

export const TipsDeepCleaning = () => {
  const { t } = useI18n();
  const slideAnim = useRef(new Animated.Value(20)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Run layout animation for smooth appearance
    AnimationHelpers.runLayoutAnimation('easeInEaseOut', 500);

    // Use the enhanced animation helper for slide-in with fade
    AnimationHelpers.slideInUpWithFade(slideAnim, fadeAnim).start();
  }, []);

  return (
    <Animated.View
      style={{
        opacity: fadeAnim,
        transform: [{ translateY: slideAnim }],
      }}
    >
      <BlockView
        testID="tipsDeepCleaning"
        row
        padding={Spacing.SPACE_16}
        border={{ width: 1, color: ColorsV2.green500 }}
        radius={BorderRadius.RADIUS_08}
        margin={{ bottom: Spacing.SPACE_16 }}
      >
        <IconImage source={IconAssets.icInfoFill} />
        <BlockView
          flex
          margin={{ left: Spacing.SPACE_16 }}
        >
          <CText>{t('TIP_DEEP_CLEANING')}</CText>
        </BlockView>
      </BlockView>
    </Animated.View>
  );
};
