/**
 * PriceIncrease Component
 *
 * Displays a warning message when price has increased due to supply and demand.
 * Includes smooth fade-in and fade-out animations.
 */
import React from 'react';
import {
  BlockView,
  BorderRadius,
  ColorsV2,
  CText,
  getIsoCodeGlobal,
  IconAssets,
  IconImage,
  ISO_CODE,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

/**
 * PriceIncrease component
 *
 * Shows a warning when prices have increased due to supply and demand.
 * Animates with fade effects for smooth appearance and disappearance.
 */
export const WaringText = () => {
  const { t } = useI18n();

  return (
    <BlockView
      radius={BorderRadius.RADIUS_08}
      border={{ width: 1, color: ColorsV2.yellow500 }}
      style={styles.container}
    >
      <IconImage source={IconAssets.icWarning} />
      <BlockView flex>
        <CText style={styles.txtInfo}>
          {getIsoCodeGlobal() === ISO_CODE.ID
            ? t('WARNING_DEEP_CLEANING_ID')
            : t('WARNING_DEEP_CLEANING')}
        </CText>
      </BlockView>
    </BlockView>
  );
};
