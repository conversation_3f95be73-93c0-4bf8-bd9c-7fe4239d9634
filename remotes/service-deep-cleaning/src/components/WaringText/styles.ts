/**
 * Styles for the PriceIncrease component
 */
import { StyleSheet } from 'react-native';
import { ColorsV2, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    flexDirection: 'row',
    padding: Spacing.SPACE_16,
    backgroundColor: ColorsV2.yellow50,
  },
  txtInfo: {
    marginHorizontal: Spacing.SPACE_16,
  },
  warningIcon: {
    width: 32,
    height: 32,
  },
});
