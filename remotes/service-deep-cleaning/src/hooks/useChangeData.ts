import { DateTimeHelpers, IDate } from '@btaskee/design-system';
import { IDuration } from '@types';

import { usePostTaskStore } from '@stores';

import { usePostTask } from './usePostTask';

export const useChangeData = () => {
  const { setDateTime, setDuration, setNumberOfTasker, setArea } =
    usePostTaskStore();
  const { getPrice } = usePostTask();

  const onChangeDuration = (newDuration: IDuration) => {
    setDuration(newDuration.duration);
    // set number of tasker and area
    setNumberOfTasker(newDuration.numberOfTaskers);
    setArea(newDuration.area);
    // get price
    getPrice();
  };

  const onChangeDateTime = (newDate: IDate) => {
    const currentState = usePostTaskStore.getState();
    const { address, date } = currentState;

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);
    const isSame = DateTimeHelpers.checkIsSame({
      firstDate: date,
      secondDate: newDate,
      timezone,
    });
    // check spam, call api with same data
    if (isSame) return null;
    // set new date time
    setDateTime(newDate);
    // get price again
    getPrice();
  };

  return {
    onChangeDuration,
    onChangeDateTime,
  };
};
