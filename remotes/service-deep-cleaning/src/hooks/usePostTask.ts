import {
  <PERSON><PERSON>,
  DateT<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Endpoint<PERSON>eys,
  getPhoneNumber,
  handleError,
  IApiError,
  IDate,
  PAYMENT_METHOD,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  useUserStore,
} from '@btaskee/design-system';
import { IDataBooking, IParamsGetPrice } from '@types';
import { debounce } from 'lodash-es';

import { RouteName } from '@navigation/RouteName';
import { usePostTaskStore } from '@stores';

import { useAppNavigation } from './useAppNavigation';
import { useI18n } from './useI18n';

export const usePostTask = () => {
  const { t } = useI18n();
  const navigation = useAppNavigation();
  const { setPrice, setLoadingPrice, resetState } = usePostTaskStore();
  const { showLoading, hideLoading } = useAppLoadingStore();
  const { user } = useUserStore();
  const { isoCode } = useAppStore();

  const { mutate: getPriceDeepCleaning } =
    useApiMutation<EndpointKeys.getPriceDeepCleaning>({
      key: EndpointKeys.getPriceDeepCleaning,
      options: {
        onMutate: () => {
          setLoadingPrice(true);
          showLoading();
        },
        onSettled: () => {
          setLoadingPrice(false);
          hideLoading();
        },
      },
    });

  const { mutate: checkTaskSameTime } = useApiMutation({
    key: EndpointKeys.checkTaskSameTime,
    options: {
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const { mutate: getOutstandingPayment } = useApiMutation({
    key: EndpointKeys.getOutstandingPayment,
  });

  const { mutate: postTaskDeepCleaning } =
    useApiMutation<EndpointKeys.postTaskDeepCleaning>({
      key: EndpointKeys.postTaskDeepCleaning,
      options: {
        onSuccess: (data) => {
          // success
          if (data?.bookingId) {
            resetState();
            navigation.popToTop();
            navigation.navigate(RouteName.PostTaskSuccess);
          }
        },
        onError: (error: IApiError) => {
          _handleErrorPostTask(error);
        },
        onMutate: () => {
          showLoading();
        },
        onSettled() {
          hideLoading();
        },
      },
    });

  const getDataPricing = () => {
    // Get the latest state directly from the store
    const currentState = usePostTaskStore.getState();
    const currentDeepCleaningState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      isAutoChooseTasker,
      service,
      paymentMethod,
      isPremium,
    } = currentState;
    const { area, numberOfTasker } = currentDeepCleaningState;

    if (!address || !date || !duration) {
      return null;
    }

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    // base info
    const task: IParamsGetPrice['task'] = {
      timezone,
      date: DateTimeHelpers.formatToString({ timezone, date }),
      autoChooseTasker: isAutoChooseTasker,
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      homeType: address?.homeType,
      duration: duration,
    };

    if (paymentMethod?.value) {
      task.payment = {
        method: paymentMethod?.value,
      };
    }
    if (area && numberOfTasker) {
      task.detailDeepCleaning = {
        area: area,
        numberOfTasker: numberOfTasker,
      };
    }
    // with promotion
    // if (promotion) {
    //   task.promotion = {
    //     code: promotion.code,
    //   };
    // } else if (paymentMethod?.promotionPaymentMethodCode) {
    //   // with promotion PaymentMethod
    //   task.promotion = {
    //     promotionPaymentMethodCode: paymentMethod?.promotionPaymentMethodCode,
    //   };
    // }
    // Check premium service
    if (isPremium) {
      task.isPremium = true;
    }
    return { task, service: { _id: service?._id }, isoCode };
  };

  const getPrice = debounce(async () => {
    // refactor data after call get price
    const data = getDataPricing();

    // data is null, no get price
    if (!data) {
      // set price is nul --> hide price button.
      return setPrice(null);
    }

    // call get price API
    getPriceDeepCleaning(data, {
      onSuccess: (result) => {
        setPrice(result);
      },
      onError: (error) => {
        handleError(error);
        setPrice(null);
      },
    });
  }, 150);

  const _refactorDataPostTask = () => {
    // Get the latest state directly from the store
    const currentState = usePostTaskStore.getState();
    const currentDeepCleaningState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      isAutoChooseTasker,
      service,
      paymentMethod,
      isApplyNoteForAllTask,
      note,
    } = currentState;
    const { area, numberOfTasker } = currentDeepCleaningState;

    const isAddressMaybeWrong = Boolean(address?.isAddressMaybeWrong);
    const isTet = service?.isTet || false;
    const city = address?.city;
    const timezone = DateTimeHelpers.getTimezoneByCity(city);

    // base task info
    const task: IDataBooking = {
      address: address?.address,
      contactName: address?.contact || user?.name,
      lat: address?.lat,
      lng: address?.lng,
      phone: address?.phoneNumber || user?.phone,
      countryCode: address?.countryCode || user?.countryCode,
      description: address?.description,
      askerId: user?._id,
      autoChooseTasker: isAutoChooseTasker,
      date: DateTimeHelpers.formatToString({
        date: date,
        timezone,
      }),
      timezone,
      deviceInfo: DeviceHelper.getDeviceInfo(),
      duration: duration,
      homeType: address?.homeType,
      houseNumber: address?.description,
      isoCode,
      payment: {
        method: paymentMethod?.value,
      },
      serviceId: service?._id,
      taskPlace: {
        country: address?.country,
        city,
        district: address?.district,
      },
      updateTaskNoteToUser: isApplyNoteForAllTask,
      shortAddress: address?.shortAddress,
    };

    // Refactor phone number - refill 0 at first
    task.phone = getPhoneNumber(task.phone || '', task.countryCode || '');

    // Địa chỉ có thể bị sai => khi book task, send to slack cho team vận hành xử lý
    if (isAddressMaybeWrong) {
      task.taskPlace.isAddressMaybeWrong = isAddressMaybeWrong;
    }

    if (isTet) {
      task.isTetBooking = true;
    }

    if (note && note?.trim()) {
      task.taskNote = note?.trim();
    }

    // promotion
    // if (promotion) {
    //   task.promotion = { code: promotion?.code };
    // } else if (paymentMethod?.promotionPaymentMethodCode) {
    //   task.promotion = { promotionPaymentMethodCode: paymentMethod?.promotionPaymentMethodCode };
    // }

    // ----- Payment -----
    // payment with card
    if (task.payment?.method === PAYMENT_METHOD.card) {
      task.payment.cardId = paymentMethod?.cardInfo?._id;
    }

    // payment with virtualAccount
    if (task.payment?.method === PAYMENT_METHOD.virtualAccount) {
      task.payment.bank = paymentMethod?.bank;
    }

    // payment with truemoney wallet
    if (
      task.payment?.method === PAYMENT_METHOD.trueMoney &&
      paymentMethod?.walletInfo
    ) {
      task.payment = { ...task.payment, ...paymentMethod?.walletInfo };
    }
    // ----- End Payment -----
    task.detailDeepCleaning = {
      numberOfTaskersDeepCleaning: numberOfTasker,
      areaDeepCleaning: area,
    };

    return task;
  };

  /**
   * @description function booking task
   * @param payload {{object}} data of task
   */
  const postTask = async () => {
    const currentState = usePostTaskStore.getState();
    const { date, service, address } = currentState;
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    // check task same time
    checkTaskSameTime(
      {
        taskDate: DateTimeHelpers.formatToString({
          date: date as IDate,
          timezone,
        }),
        serviceId: service?._id || '',
      },
      {
        onSuccess: (data) => {
          _addTask({ isExistTask: !data });
        },
      },
    );
  };

  /**
   * @description function booking task
   * @param payload {{object}} data of task
   */
  const _addTask = debounce(
    async ({ isExistTask }: { isExistTask: boolean }) => {
      const dataTask = _refactorDataPostTask();
      // time ok
      if (isExistTask) {
        // call api book task
        postTaskDeepCleaning(dataTask);
        return;
      }

      // same time, alert for user
      return Alert.alert.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('TASK_SAME_TIME_MESSAGE'),
        actions: [
          { text: t('CLOSE'), style: 'cancel' },
          {
            text: t('OK'),
            onPress: async () => {
              // wait modal close
              setTimeout(async () => {
                postTaskDeepCleaning(dataTask);
              }, 300);
            },
          },
        ],
      });
    },
    300,
  );

  const _handleErrorPostTask = (error: IApiError) => {
    if (error && error?.code === 'OUTSTANDING_PAYMENT_STATUS_NEW') {
      Alert.alert?.open?.({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('OUTSTANDING_PAYMENT_STATUS_NEW'),
        actions: [
          {
            text: t('PAYMENT_TOP_UP'),
            onPress: () => getOutstandingPayment({}),
          },
        ],
      });
      return;
    }
    handleError(error);
  };

  return { getPrice, postTask };
};
