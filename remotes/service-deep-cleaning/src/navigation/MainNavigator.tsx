import '../i18n';

import React, { useCallback, useEffect, useMemo } from 'react';
import {
  BlockView,
  ColorsV2,
  CText,
  getDefaultPaymentMethod,
  IconAssets,
  IconImage,
  NavBar,
  SERVICES,
  Spacing,
  useSettingsStore,
} from '@btaskee/design-system';
import {
  createNativeStackNavigator,
  NativeStackHeaderProps,
  type NativeStackNavigationOptions,
} from '@react-navigation/native-stack';

import { useI18n } from '@hooks';
import { MainStackParamList } from '@navigation/type';
import {
  ChooseAddress,
  ChooseDateTime,
  ChooseDuration,
  ConfirmAndPayment,
  PostTaskSuccess,
  WorkingProcess,
} from '@screens';
import { usePostTaskStore } from '@stores';

import { RouteName } from './RouteName';

// Extract component outside of render to prevent reconciliation issues
interface AddressTitleProps {
  shortAddress?: string;
  address?: string;
}

const AddressTitle: React.FC<AddressTitleProps> = React.memo(
  ({ shortAddress, address }) => {
    if (!shortAddress && !address) {
      return null;
    }

    return (
      <BlockView
        flex
        row
        horizontal
      >
        <IconImage
          source={IconAssets.icLocation}
          size={24}
          color={ColorsV2.red500}
        />
        <BlockView margin={{ left: Spacing.SPACE_08 }}>
          <CText color={ColorsV2.neutral400}>{shortAddress}</CText>
          <CText
            fontFamily="bold"
            numberOfLines={1}
            margin={{ right: Spacing.SPACE_16 }}
            color={ColorsV2.neutral800}
          >
            {address}
          </CText>
        </BlockView>
      </BlockView>
    );
  },
);

AddressTitle.displayName = 'AddressTitle';

const Stack = createNativeStackNavigator<MainStackParamList>();

const MainNavigator = () => {
  const { t } = useI18n();

  const { address, setService, setPaymentMethod } = usePostTaskStore();
  const settings = useSettingsStore().settings;

  useEffect(() => {
    initData();
  }, []);

  const initData = async () => {
    const deepCleaningService = settings?.services?.find(
      (service) => service?.name === SERVICES.DEEP_CLEANING,
    );

    setService(deepCleaningService);
    setPaymentMethod(
      getDefaultPaymentMethod({ serviceName: SERVICES.DEEP_CLEANING }),
    );
  };

  // Memoize the address title component to avoid recreation
  const addressTitleComponent = useMemo(
    () => (
      <AddressTitle
        shortAddress={address?.shortAddress}
        address={address?.address}
      />
    ),
    [address?.shortAddress, address?.address],
  );

  // Screen options following the established pattern across all microservices
  const screenOptions = useCallback(
    ({ navigation }: any): NativeStackNavigationOptions => ({
      headerShown: true,
      animation: 'slide_from_right',
      animationDuration: 200,
      contentStyle: { backgroundColor: ColorsV2.neutralWhite },
      // eslint-disable-next-line react/no-unstable-nested-components
      header: (props: NativeStackHeaderProps) => {
        // Get the title from options
        const getTitle = () => {
          if (
            props?.options?.headerTitle &&
            typeof props.options.headerTitle === 'function'
          ) {
            // @ts-ignore - React Navigation headerTitle function compatibility
            return props.options.headerTitle();
          }
          if (
            props?.options?.headerTitle &&
            typeof props.options.headerTitle === 'string'
          ) {
            return props.options.headerTitle;
          }
          if (props?.options?.title) {
            return props.options.title;
          }
          return '';
        };

        return (
          <NavBar
            // @ts-ignore - NavBar title accepts ReactNode but types are strict
            title={getTitle()}
            backgroundColor={ColorsV2.neutralWhite}
            onGoBack={() => navigation.goBack()}
            isShadow={true}
          />
        );
      },
    }),
    [],
  );

  return (
    <Stack.Navigator
      screenOptions={screenOptions}
      initialRouteName={RouteName.ChooseAddress}
    >
      <Stack.Screen
        name={RouteName.ChooseAddress}
        component={ChooseAddress}
        options={{ title: t('LIST_OF_LOCATIONS') }}
      />
      <Stack.Screen
        name={RouteName.ChooseDuration}
        component={ChooseDuration}
        options={{ headerTitle: () => addressTitleComponent }}
      />
      <Stack.Screen
        name={RouteName.ChooseDateTime}
        component={ChooseDateTime}
        options={{ title: t('WORK_TIME_TITLE') }}
      />
      <Stack.Screen
        name={RouteName.ConfirmAndPayment}
        component={ConfirmAndPayment}
        options={{ title: t('PT2_CONFIRM_HEADER_TITLE') }}
      />
      <Stack.Screen
        name={RouteName.PostTaskSuccess}
        component={PostTaskSuccess}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={RouteName.WorkingProcess}
        component={WorkingProcess}
        options={{ title: t('WORKING_PROCESS') }}
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;
