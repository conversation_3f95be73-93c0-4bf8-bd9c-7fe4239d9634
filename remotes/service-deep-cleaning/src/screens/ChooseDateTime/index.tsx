import React, { useMemo } from 'react';
import { ScrollView } from 'react-native';
import {
  Alert,
  BlockView,
  DatePicker,
  DatePickerTet,
  DateTimeHelpers,
  getCurrency,
  NotePostTask,
  PostTaskHelpers,
  PriceButton,
  PriceIncrease,
  Spacing,
  TimePicker,
  useSettingsStore,
} from '@btaskee/design-system';
import { useIsFocused } from '@react-navigation/native';
import { get } from 'lodash-es';

import { useAppNavigation, useChangeData, useI18n } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { usePostTaskStore } from '@stores';

import styles from './styles';

// Import component
export const ChooseDateTime = () => {
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const isFocused = useIsFocused();

  const { onChangeDateTime } = useChangeData();
  const {
    address,
    date,
    duration,
    note,
    setNote,
    setIsApplyNoteForAllTask,
    isApplyNoteForAllTask,
    price,
    service,
  } = usePostTaskStore();
  const { settings } = useSettingsStore();

  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

  const onConfirmed = () => {
    // check time before 60min
    if (
      !PostTaskHelpers.validateDateTime(
        timezone,
        date,
        settings?.settingSystem?.minPostTaskTime,
      )
    ) {
      return Alert?.alert?.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('POSTTASK_STEP2_ERROR_TIME', {
          t: settings?.settingSystem?.minPostTaskTime,
        }),
        actions: [{ text: t('PT2_POPUP_ERROR_TIME_CLOSE') }],
      });
    }

    // check posting limit, ex 6AM - 10PM
    const postingLimits = service?.postingLimits;
    if (
      !PostTaskHelpers.checkTimeValidFromService(
        timezone,
        date,
        duration,
        postingLimits,
      )
    ) {
      const postingLimitsFormat = PostTaskHelpers.formatPostingLimits({
        timezone,
        postingLimits,
      });
      return Alert?.alert?.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('PT2_POPUP_ERROR_TIME_INVALID_CONTENT', {
          from: postingLimitsFormat.from,
          to: postingLimitsFormat.to,
        }),
        actions: [{ text: t('PT2_POPUP_ERROR_TIME_INVALID_CLOSE') }],
      });
    }
    // data ok
    onGotoStep4();
  };

  const onGotoStep4 = () => {
    // data ok
    // setStepPostTask(TRACKING_STEP.STEP_4);
    navigation.navigate(RouteName.ConfirmAndPayment);
  };

  const shouldRenderChooseDateTime = useMemo(() => {
    return (
      <BlockView>
        {get(service, 'isTet', null) ? (
          // Tet booking need a calendar to choose easily
          <DatePickerTet
            service={service}
            date={date}
            onChangeDateTime={onChangeDateTime}
            settingSystem={settings?.settingSystem}
            timezone={timezone}
          />
        ) : (
          // Normal date picker
          <DatePicker
            value={date}
            onChange={onChangeDateTime}
            settingSystem={settings?.settingSystem}
            timezone={timezone}
          />
        )}
        <TimePicker
          value={date}
          onChange={onChangeDateTime}
          settingSystem={settings?.settingSystem}
          timezone={timezone}
        />
      </BlockView>
    );
  }, [date, service?.isTet, isFocused, timezone]);

  return (
    <BlockView
      inset={['bottom']}
      style={styles.container}
    >
      <BlockView style={styles.content}>
        <ScrollView
          testID="scrollChooseDateTime"
          contentContainerStyle={styles.containerScroll}
          showsVerticalScrollIndicator={false}
        >
          {shouldRenderChooseDateTime}
          <PriceIncrease
            price={price}
            address={address}
            service={service}
            increaseReasons={get(price, 'increaseReasons', false)}
            isShow={Boolean(get(price, 'isIncrease', false))}
          />

          <NotePostTask
            setNote={setNote}
            value={note}
            service={service}
            isApplyNoteForAllTask={isApplyNoteForAllTask}
            setNoteForAllTask={setIsApplyNoteForAllTask}
            containerStyle={{ marginTop: Spacing.SPACE_24 }}
            placeholder={t('SERVICE_NOTE_CONTENT')}
            title={t('LABEL_NOTE_FOR_TASKER')}
            description={t('TASK_NOTE_DESCRIPTION')}
          />
        </ScrollView>
      </BlockView>

      <PriceButton
        testID="btnNextStep3"
        onPress={onConfirmed}
        fromScreen={service?.name}
        pricePostTask={price}
      />
    </BlockView>
  );
};
