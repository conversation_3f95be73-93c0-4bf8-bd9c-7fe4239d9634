import React from 'react';
import { ScrollView } from 'react-native';
import {
  BlockView,
  checkSupportCity,
  ConditionView,
  CText,
  NotSupportCity,
  PostTaskHelpers,
  PriceButton,
  ProcessButton,
  SERVICES,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';

import { Duration, TipsDeepCleaning, WaringText } from '@components';
import { useAppNavigation, useChangeData, useI18n } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

export const ChooseDuration = () => {
  const { t } = useI18n();
  const navigation = useAppNavigation();
  const { service, address, setDateTime, duration, price, numberOfTasker } =
    usePostTaskStore();
  const { onChangeDuration } = useChangeData();

  React.useEffect(() => {
    // Did mount here
    // Check selected service difference with previous service: reset states before begining
    // The same service: keep state for user continue the booking
    setDateTime(
      PostTaskHelpers.getDefaultDateTime(
        {
          serviceName: SERVICES.DEEP_CLEANING,
          defaultTaskTime: service?.defaultTaskTime,
        },
        service?.defaultTaskTime,
        address?.city,
      ),
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const _onConfirmed = () => {
    // handleTrackingAction(TRACKING_ACTION.Next);

    // if (checkSupportCityAndAlert(address?.city)) {
    // setStepPostTask(TRACKING_STEP.STEP_3);
    navigation.navigate(RouteName.ChooseDateTime);
    // }
  };

  const onOpenWorkingProgress = () => {
    navigation.navigate(RouteName.WorkingProcess);
  };

  // Check support city
  if (!checkSupportCity(service?.city, address?.city)) {
    return <NotSupportCity />;
  }

  return (
    <BlockView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.containerScroll}
        showsVerticalScrollIndicator={false}
      >
        <BlockView>
          <CText
            bold
            style={styles.txtPanel}
          >
            {t('DURATION')}
          </CText>
          <CText style={styles.txtDescription}>
            {t('DURATION_DESCRIPTION')}
          </CText>
        </BlockView>
        <Duration
          onChange={onChangeDuration}
          numberOfTasker={numberOfTasker}
        />

        {/* <PremiumOptional
          isPremium={props.isPremium}
          onChangePremium={onChangePremium}
          txtPropStyle={styles.txtPanel}
        /> */}
        <ConditionView
          condition={Boolean(duration && numberOfTasker)}
          viewTrue={<TipsDeepCleaning />}
        />
        <WaringText />
        <SizedBox height={Spacing.SPACE_16} />
        <ProcessButton onPress={onOpenWorkingProgress} />
      </ScrollView>
      <PriceButton
        testID={'btnNextStep2'}
        onPress={_onConfirmed}
        pricePostTask={price}
      />
    </BlockView>
  );
};
