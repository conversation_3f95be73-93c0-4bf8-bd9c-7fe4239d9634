import { StyleSheet } from 'react-native';
import {
  ColorsV2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 0,
    backgroundColor: ColorsV2.neutralBackground,
  },
  containerScroll: {
    // flex: 1,
    padding: Spacing.SPACE_16,
    paddingBottom: DeviceHelper.WINDOW.HEIGHT * 0.2,
    backgroundColor: ColorsV2.neutralWhite,
  },
  txtPanel: {
    fontSize: FontSizes.SIZE_20,
    marginBottom: Spacing.SPACE_16,
  },
  txtDescription: {
    marginBottom: Spacing.SPACE_24,
  },
});
