import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  <PERSON><PERSON>Helper,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

const width = DeviceHelper.WINDOW.WIDTH;
export default StyleSheet.create({
  container: {
    backgroundColor: ColorsV2.neutralBackground,
    flex: 1,
  },
  modalTitleStyle: {
    textAlign: 'center',
    color: ColorsV2.green500,
    fontSize: FontSizes.SIZE_20,
  },
  txtContent: {
    marginTop: Spacing.SPACE_08,
    textAlign: 'center',
    color: ColorsV2.neutral400,
    fontSize: Spacing.SPACE_12,
    lineHeight: 18,
  },
  btnActionBox: {
    paddingHorizontal: Spacing.SPACE_16,
    marginTop: Spacing.SPACE_16,
    paddingVertical: Spacing.SPACE_16,
  },
  touchableFollowTask: {
    minHeight: 56,
    borderRadius: Spacing.SPACE_16,
    backgroundColor: ColorsV2.green500,
    justifyContent: 'center',
    marginBottom: Spacing.SPACE_16,
  },
  txtBtnFollowTask: {
    textAlign: 'center',
    fontSize: FontSizes.SIZE_14,
    color: ColorsV2.neutralWhite,
  },
  touchableGoHome: {
    minHeight: 56,
    borderRadius: Spacing.SPACE_16,
    justifyContent: 'center',
    backgroundColor: ColorsV2.neutral100,
  },
  txtBtnGoHome: {
    textAlign: 'center',
    color: ColorsV2.green500,
    fontSize: FontSizes.SIZE_14,
  },
  txtRelatedService: {
    fontSize: FontSizes.SIZE_18,
  },
  txtBTaskee: {
    color: ColorsV2.orange500,
  },
  iconTick: {
    width: Math.round(width / 2),
    height: Math.round(width / 2),
  },
  txtContext: {
    marginTop: Spacing.SPACE_08,
    fontSize: Spacing.SPACE_12,
  },
  contentContainerStyle: {
    paddingHorizontal: 8,
  },
  containerRelatedServices: {
    paddingBottom: Spacing.SPACE_20,
  },
  thumbnailService: {
    width: '100%',
    height: width / 5,
  },
  txtServiceName: {
    fontSize: Spacing.SPACE_12,
  },
  boxService: {
    borderRadius: BorderRadius.RADIUS_08,
    borderWidth: 1,
    borderColor: ColorsV2.neutral100,
    margin: Spacing.SPACE_08,
    width: width / 2.5,
    overflow: 'hidden',
  },
  wrapServiceName: {
    padding: Spacing.SPACE_08,
  },
  iconNew: {
    width: 60,
    height: 30,
    position: 'absolute',
    right: 0,
    top: 0,
  },
  wrapContent: {
    flex: 2,
  },
  moreServiceItem: {
    width: '33%',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
