import React, { useMemo } from 'react';
import { ScrollView } from 'react-native';
import {
  BlockView,
  CText,
  FastImage,
  getLocaleGlobal,
  getTextWithLocale,
  Markdown,
} from '@btaskee/design-system';
import { get } from 'lodash-es';

import { useI18n } from '@hooks';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

export const WorkingProcess = () => {
  const { service } = usePostTaskStore();
  const data = service?.workingProcessV2 || null;
  const { t } = useI18n();

  // useEffect(() => {
  //   trackingServiceView({
  //     campaignName: service?.isTet ? configSpecialPreBooking?.name : null,
  //     screenName: TrackingScreenNames.WorkingProcess,
  //     serviceName: SERVICES.DEEP_CLEANING,
  //     entryPoint: TrackingScreenNames.DetailInformation,
  //     isTetBooking: service?.isTet,
  //   });
  // }, [configSpecialPreBooking?.name, service?.isTet]);

  const renderOverview = useMemo(() => {
    let overView = get(data, `overview`, null);
    if (overView) {
      overView = overView?.find?.((e) => e.name === getLocaleGlobal());
    }
    if (!overView) {
      return null;
    }

    return (
      <BlockView style={styles.overview}>
        <CText
          bold
          style={styles.txtPanel}
        >
          {t('OVERVIEW')}
        </CText>
        {overView?.value?.map((e, index) => (
          <Markdown
            key={index?.toString()}
            text={e}
            textStyle={styles.txtDescription}
          />
        ))}
      </BlockView>
    );
  }, [data, t]);

  const renderJobList = React.useMemo(() => {
    const detail = get(data, `detail`, null);
    if (!detail) {
      return null;
    }
    const result = [];
    for (const room in detail) {
      result.push(
        <BlockView
          row
          style={[styles.item, room < detail.length - 1 && styles.borderBottom]}
          key={room}
        >
          <BlockView>
            <FastImage
              style={styles.image}
              source={{ uri: detail[room].icon }}
            />
          </BlockView>
          <BlockView style={styles.rightContent}>
            <CText
              bold
              style={styles.txtTitle}
            >
              {getTextWithLocale(detail[room].text, getLocaleGlobal())}
            </CText>
            {detail[room].workToDo.map((e, index) => (
              <CText
                key={index}
                style={styles.txtDescription}
              >
                {getTextWithLocale(e, getLocaleGlobal())}
              </CText>
            ))}
          </BlockView>
        </BlockView>,
      );
    }
    return <BlockView style={styles.wrapJobList}>{result}</BlockView>;
  }, [data]);

  if (!data || !service) {
    return null;
  }

  return (
    <BlockView style={styles.container}>
      <ScrollView
        scrollIndicatorInsets={{ right: 1 }}
        contentContainerStyle={styles.containerScroll}
      >
        {renderOverview}
        {renderJobList}
      </ScrollView>
    </BlockView>
  );
};
