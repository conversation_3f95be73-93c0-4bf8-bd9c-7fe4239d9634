import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  <PERSON><PERSON><PERSON>elper,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

const SIZE_IMAGE = Math.round(DeviceHelper.WINDOW.WIDTH / 7);

export const styles = StyleSheet.create({
  item: {
    padding: Spacing.SPACE_16,
  },
  borderBottom: {
    borderBottomColor: ColorsV2.neutral100,
    borderBottomWidth: 1,
  },
  txtTitle: {
    marginBottom: Spacing.SPACE_04,
  },
  rightContent: {
    flex: 1,
    paddingLeft: Spacing.SPACE_16,
  },
  wrapJobList: {
    backgroundColor: ColorsV2.neutralWhite,
    borderRadius: BorderRadius.RADIUS_08,
    borderColor: ColorsV2.neutral100,
    borderWidth: 1,
    marginTop: Spacing.SPACE_32,
  },
  container: {
    flex: 1,
    padding: 0,
    backgroundColor: ColorsV2.neutralBackground,
  },
  containerScroll: {
    padding: Spacing.SPACE_16,
    paddingBottom: DeviceHelper.WINDOW.HEIGHT * 0.2,
    backgroundColor: ColorsV2.neutralWhite,
  },
  txtPanel: {
    fontSize: FontSizes.SIZE_20,
    marginBottom: Spacing.SPACE_08,
  },
  image: {
    width: SIZE_IMAGE,
    height: SIZE_IMAGE,
  },
  overview: {},
  txtDescription: {
    color: ColorsV2.neutral800,
  },
});
