import {
  AppStorage,
  createZustand,
  IAddons,
  IAddress,
  IDate,
  IPrice,
  IService,
  Maybe,
  Requirement,
} from '@btaskee/design-system';
import { createJSONStorage, persist } from 'zustand/middleware';

interface AppState {
  address: IAddress;
  duration: number;
  requirements: Requirement[];
  isPremium: boolean;
  isAutoChooseTasker: boolean;
  isFavouriteTasker: boolean;
  gender: string;
  pet: any;
  addons: IAddons[];
  date: Maybe<IDate>;
  note: string;
  isApplyNoteForAllTask: boolean;
  price: Maybe<IPrice>;
  service: Maybe<IService>;
  paymentMethod: any;
  promotion: any;
  isLoadingPrice: boolean;
  loadingPostTask: boolean;
  homeNumber: string;
  isFirstOpen: boolean;

  numberOfTasker: number;
  area: number;

  setAddress: (address: IAddress) => void;
  setDuration: (duration: number) => void;
  setRequirements: (requirements: Requirement[]) => void;
  setIsPremium: (isPremium: boolean) => void;
  setIsAutoChooseTasker: (isAutoChooseTasker: boolean) => void;
  setGender: (gender: string) => void;
  setAddons: (addons: IAddons[]) => void;
  setPet: (pet: any) => void;
  setDateTime: (date?: IDate) => void;
  setNote: (note: string) => void;
  setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) => void;
  setPrice: (price?: Maybe<IPrice>) => void;
  setHomeNumber: (homeNumber: string) => void;
  setPaymentMethod: (paymentMethod: any) => void;
  setPromotion: (promotion: any) => void;
  setLoadingPrice: (isLoadingPrice: boolean) => void;
  setService: (service: Maybe<IService>) => void;
  setLoadingPostTask: (loadingPostTask: boolean) => void;
  setIsFirstOpen: (isFirstOpen: boolean) => void;

  setNumberOfTasker: (numberOfTasker: number) => void;
  setArea: (area: number) => void;
  resetState: () => void;
}

export const usePostTaskStore = createZustand<AppState>()(
  persist(
    (set) => ({
      address: {},
      duration: 0,
      requirements: [],
      isPremium: false,
      isAutoChooseTasker: true,
      isFavouriteTasker: false,
      gender: '',
      pet: '',
      addons: [],
      date: null,
      note: '',
      isApplyNoteForAllTask: false,
      homeNumber: '',
      price: null,
      service: null,
      paymentMethod: null,
      promotion: null,
      isLoadingPrice: false,
      loadingPostTask: false,
      relatedTask: null,
      isFirstOpen: true,
      numberOfTasker: 0,
      area: 0,

      setLoadingPostTask: (loadingPostTask: boolean) =>
        set({ loadingPostTask: loadingPostTask }),
      setAddress: (address: IAddress) => set({ address: address }),
      setDuration: (duration: number) => set({ duration: duration }),
      setRequirements: (requirements: Requirement[]) =>
        set({ requirements: requirements }),
      setIsPremium: (isPremium: boolean) => set({ isPremium: isPremium }),
      setIsAutoChooseTasker: (isAutoChooseTasker: boolean) =>
        set({ isAutoChooseTasker: isAutoChooseTasker }),
      setGender: (gender: string) => set({ gender: gender }),
      setAddons: (addons: IAddons[]) => set({ addons: addons }),
      setPet: (pet: any) => set({ pet: pet }),
      setDateTime: (date?: IDate) => set({ date: date }),
      setNote: (note: string) => set({ note: note }),
      setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) =>
        set({ isApplyNoteForAllTask: isApplyNoteForAllTask }),
      setPrice: (price?: Maybe<IPrice>) => set({ price: price }),
      setHomeNumber: (homeNumber: string) => set({ homeNumber: homeNumber }),
      setPaymentMethod: (paymentMethod: any) =>
        set({ paymentMethod: paymentMethod }),
      setPromotion: (promotion: any) => set({ promotion: promotion }),
      setLoadingPrice: (isLoadingPrice: boolean) =>
        set({ isLoadingPrice: isLoadingPrice }),
      setService: (service: Maybe<IService>) => set({ service: service }),
      setIsFirstOpen: (isFirstOpen: boolean) =>
        set({ isFirstOpen: isFirstOpen }),

      setNumberOfTasker: (numberOfTasker: number) =>
        set({ numberOfTasker: numberOfTasker }),
      setArea: (area: number) => set({ area: area }),
      resetState: () =>
        set({
          address: {},
          duration: 0,
          requirements: [],
          isPremium: false,
          isAutoChooseTasker: true,
          isFavouriteTasker: false,
          gender: '',
          pet: '',
          addons: [],
          date: null,
          note: '',
          isApplyNoteForAllTask: false,
          homeNumber: '',
          price: null,
          service: null,
          paymentMethod: null,
          promotion: null,
          isLoadingPrice: false,
          loadingPostTask: false,
          isFirstOpen: true,
          numberOfTasker: 0,
          area: 0,
        }),
    }),
    {
      name: 'deep-cleaning-storage',
      storage: createJSONStorage(() => AppStorage.zustandStorage),
      version: 1,
      partialize: (state) => ({ isFirstOpen: state.isFirstOpen }),
    },
  ),
);
