import React, { useMemo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>iew,
  Card,
  ColorsV2,
  ConditionView,
  CText,
  DeviceHelper,
  FastImage,
  formatMoney,
  getCurrency,
  IconAssets,
  IconImage,
  Requirement,
  Spacing,
  TouchableOpacity,
  useAppStore,
} from '@btaskee/design-system';
import { isEmpty, last } from 'lodash-es';

import { useChangeData, useI18n } from '@hooks';
import { imgCleaningGlass } from '@images';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

const width = DeviceHelper.WINDOW.WIDTH;
const TYPE_CLEANING_GLASS = 6;

export const AddOnServices = () => {
  const { t } = useI18n();
  const { service, duration, requirements } = usePostTaskStore();

  const { handleAddOnService } = useChangeData();
  const currency = getCurrency();
  const locale = useAppStore()?.locale;

  const checkActive = useMemo(
    () => (requirement: Requirement) => {
      const isExist =
        requirements && requirements.find((e) => requirement.type === e.type);
      return Boolean(isExist);
    },
    [requirements],
  );

  const requirementsData = service?.requirements || [];

  const getCostByDuration = (
    costByDuration?: Requirement['costByDuration'],
  ) => {
    if (isEmpty(costByDuration)) {
      return 0;
    }
    const durationStep = costByDuration?.find(
      (step) => step?.duration === duration,
    );
    if (!isEmpty(durationStep)) {
      return durationStep?.cost;
    }
    return last(costByDuration)?.cost;
  };

  // Hiển thị modal chi tiết lau kính
  const showDescriptionCleaningGlass = () => {
    return Alert.alert.open({
      title: t('REQUIREMENTS_CLEANING_GLASS'),
      message: (
        <BlockView>
          <BlockView style={styles.wrapImage}>
            <FastImage
              source={imgCleaningGlass}
              style={styles.imageDescription}
            />
          </BlockView>
          <CText margin={{ top: Spacing.SPACE_16, bottom: Spacing.SPACE_08 }}>
            {t('DESCRIPTION_CLEANING_GLASS_1')}
          </CText>
          <CText>{t('DESCRIPTION_CLEANING_GLASS_2')}</CText>
        </BlockView>
      ),
      actions: [{ text: t('PT1_DETAIL_CHOOSE_MANUAL_UNDERSTOOD') }],
    });
  };

  const shouldRenderService = React.useMemo(() => {
    return requirementsData?.map((requirement, index) => {
      // check is choose requirement
      const active = checkActive(requirement);
      const costText = requirement.duration
        ? `+${t('DURATION_PER_TASKER', {
            t: requirement.duration,
          })}`
        : `+${t('MONEY_PER_TASKER', {
            t1: formatMoney(getCostByDuration(requirement?.costByDuration)),
            t2: currency,
          })}`;
      return (
        <BlockView
          style={{ width: Math.round(width / 4) }}
          key={index}
        >
          <TouchableOpacity
            testID={`chooseServies-${index}`}
            onPress={() => handleAddOnService(requirement)}
          >
            <Card
              style={[styles.cardOption, active ? styles.borderActive : {}]}
            >
              <IconImage
                style={styles.image}
                source={{ uri: requirement.icon }}
                color={active ? ColorsV2.orange500 : ColorsV2.neutral400}
              />
            </Card>
          </TouchableOpacity>
          <BlockView
            row
            center
          >
            <CText
              bold
              center
              numberOfLines={2}
              style={[styles.txtName, active ? styles.textActive : {}]}
            >
              {requirement?.text?.[locale]}
            </CText>
            <ConditionView
              condition={Boolean(requirement?.type === TYPE_CLEANING_GLASS)}
              viewTrue={
                <TouchableOpacity
                  onPress={showDescriptionCleaningGlass}
                  hitSlop={10}
                >
                  <IconImage
                    source={IconAssets.icQuestion}
                    size={12}
                  />
                </TouchableOpacity>
              }
            />
          </BlockView>
          <CText
            center
            style={[styles.txtPrice, active ? styles.textActive : {}]}
          >
            {costText}
          </CText>
        </BlockView>
      );
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [requirementsData, duration, requirements]);

  if (isEmpty(requirementsData)) {
    return null;
  }

  return (
    <BlockView style={styles.containerAddOnService}>
      <CText
        bold
        style={styles.txtPanel}
      >
        {t('SV_HC_SCR2_DETAIL_EXTRA_SV_TITLE')}
      </CText>
      <CText style={styles.txtDescription}>
        {t('SV_HC_SCR2_DETAIL_EXTRA_SV_NOTE')}
      </CText>
      <BlockView
        row
        style={styles.content}
      >
        {shouldRenderService}
      </BlockView>
    </BlockView>
  );
};
