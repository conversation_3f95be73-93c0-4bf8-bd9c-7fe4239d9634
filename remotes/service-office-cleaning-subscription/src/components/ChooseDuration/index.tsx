import React, { useState } from 'react';
import { TouchableOpacity } from 'react-native';
import {
  BlockView,
  CText,
  getTextWithLocale,
  IOptionAreaOfficeCleaning,
  ISO_CODE,
  useAppStore,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useChangeData, useI18n } from '@hooks';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

export const ChooseDuration = () => {
  const { t } = useI18n();
  const { isoCode } = useAppStore();

  const { service, detailOfficeCleaning } = usePostTaskStore();
  const { handleChooseDuration } = useChangeData();

  const [typeOfDuration, setTypeOfDuration] = useState(0);

  const areas = service?.detailService?.officeCleaning?.areas;

  const _renderStep = ({
    options,
  }: {
    options?: IOptionAreaOfficeCleaning[];
  }) => {
    if (isEmpty(options)) {
      return null;
    }
    return options?.map((item, index) => (
      <TouchableOpacity
        testID={`area-${index}`}
        key={'area_' + item?.area}
        style={
          item?.area === detailOfficeCleaning?.area
            ? styles.activeItemDuration
            : styles.inactiveItemDuration
        }
        onPress={() => handleChooseDuration(item)}
      >
        <CText>
          {t(
            isoCode === ISO_CODE.TH
              ? 'ITEM_AREA_OFFICE_CLEANING_TH'
              : 'ITEM_AREA_OFFICE_CLEANING',
            { t: item?.area },
          )}
        </CText>
        <CText>
          {t('ITEM_DURATION_OFFICE_CLEANING', {
            t1: item?.numberOfTaskers,
            t2: item?.duration,
          })}
        </CText>
      </TouchableOpacity>
    ));
  };

  const _renderTypeDuration = (): React.ReactNode => {
    if (isEmpty(areas)) {
      return null;
    }
    return areas?.map((item, index) => (
      <TouchableOpacity
        key={'type_' + item?.name}
        onPress={() => {
          setTypeOfDuration(index);
        }}
        style={
          typeOfDuration === index
            ? styles.activeDuration
            : styles.inactiveDuration
        }
      >
        <CText center>{getTextWithLocale(item?.text)}</CText>
      </TouchableOpacity>
    ));
  };

  // Check empty areas
  if (isEmpty(areas)) {
    return null;
  }

  return (
    <BlockView style={styles.container}>
      <CText
        bold
        style={styles.txtTitle}
      >
        {t('CHOOSE_DURATION_TITLE')}
      </CText>
      <CText>{t('SV_HC_SCR2_DETAIL_DURATION_TITLE_NOTE')}</CText>

      <BlockView style={styles.durationList}>
        <BlockView
          row
          jBetween
        >
          {_renderTypeDuration()}
        </BlockView>
        <BlockView
          flex
          style={styles.wrapItemDuration}
        >
          {_renderStep(areas?.[typeOfDuration] || { options: [] })}
        </BlockView>
      </BlockView>
    </BlockView>
  );
};
