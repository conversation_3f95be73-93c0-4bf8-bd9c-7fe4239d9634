import React from 'react';
import {
  BlockView,
  Card,
  ColorsV2,
  ConditionView,
  CText,
  DateTimeHelpers,
  DurationWithGMT,
  IDate,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';
import { usePostTaskStore } from '@stores';

import styles from './styles';
import { WorkingTime } from './working-time';

export const TaskDetail = () => {
  const { t } = useI18n();

  const { note, startDate, duration, detailOfficeCleaning, address } =
    usePostTaskStore();

  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

  /**
   * Renders workload information based on selected duration
   */
  const shouldRenderDuration = React.useMemo(() => {
    return (
      <BlockView style={styles.group}>
        <CText style={styles.txtVLabel}>{t('WORK_IN')}</CText>

        <DurationWithGMT
          testID={'duration'}
          style={styles.txtValue}
          timezone={timezone}
          duration={duration}
          date={startDate as IDate}
        />
      </BlockView>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [duration, startDate, timezone]);

  const shouldRenderNote = React.useMemo(() => {
    if (!note) {
      return null;
    }
    return (
      <BlockView
        row
        margin={{ top: Spacing.SPACE_08 }}
        padding={{ top: Spacing.SPACE_16 }}
        border={{ top: { width: 1, color: ColorsV2.neutral100 } }}
      >
        <CText style={styles.txtVLabel}>{t('LABEL_NOTE_FOR_TASKER')}</CText>
        <CText
          testID={'taskNote'}
          style={styles.txtValue}
        >
          {note}
        </CText>
      </BlockView>
    );
  }, [note, t]);

  return (
    <BlockView>
      <BlockView
        testID="taskInfo"
        style={styles.panel}
      >
        <CText
          bold
          style={styles.txtPanel}
        >
          {t('TASK_INFO')}
        </CText>
      </BlockView>
      <Card>
        <BlockView>
          <WorkingTime />
          <SizedBox
            height={1}
            color={ColorsV2.neutral50}
            margin={{ top: Spacing.SPACE_12, bottom: Spacing.SPACE_16 }}
          />
          <CText
            bold
            style={styles.subPanel}
          >
            {t('TASK_DETAIL')}
          </CText>

          {shouldRenderDuration}
          <ConditionView
            condition={Boolean(detailOfficeCleaning?.area)}
            viewTrue={
              <BlockView style={styles.wrapItemDetail}>
                <CText style={styles.txt_dataItem}>{t('WORKLOAD')}</CText>
                <CText style={styles.txt_value}>
                  {t('AREA_ITEM', {
                    t: detailOfficeCleaning?.area,
                  })}
                </CText>
              </BlockView>
            }
          />
          <ConditionView
            condition={Boolean(detailOfficeCleaning?.numberOfTaskers)}
            viewTrue={
              <BlockView style={styles.wrapItemDetail}>
                <CText style={styles.txt_dataItem}>
                  {t('NUMBER_OF_TASKERS')}
                </CText>
                <CText style={styles.txt_value}>
                  {t('NUMBER_OF_PEOPLE', {
                    t: detailOfficeCleaning?.numberOfTaskers,
                  })}
                </CText>
              </BlockView>
            }
          />
          {shouldRenderNote}
        </BlockView>
      </Card>
    </BlockView>
  );
};
