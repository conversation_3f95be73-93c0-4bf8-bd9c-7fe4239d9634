import React from 'react';
import {
  BlockView,
  CText,
  DateTimeHelpers,
  IDate,
  TypeFormatDate,
} from '@btaskee/design-system';
import { capitalize } from 'lodash-es';

import { useI18n } from '@hooks';
import { usePostTaskStore } from '@stores';

import styles from './styles';

export const WorkingTime = () => {
  const { t } = useI18n();
  const { startDate, endDate, price, address } = usePostTaskStore();
  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

  return (
    <BlockView>
      <BlockView>
        <CText
          bold
          style={styles.subPanel}
        >
          {t('TIME_TO_WORK')}
        </CText>
      </BlockView>
      <BlockView style={styles.group}>
        <CText style={styles.txtVLabel}>{t('START_DATE')}</CText>
        <CText
          testID="startDate"
          style={styles.txtValue}
        >
          {capitalize(
            DateTimeHelpers.formatToString({
              timezone,
              date: startDate as IDate,
              typeFormat: TypeFormatDate.DateFullWithDay,
            }),
          )}
        </CText>
      </BlockView>
      <BlockView style={styles.group}>
        <CText style={styles.txtVLabel}>{t('END_DATE')}</CText>
        <CText
          testID="endDate"
          style={styles.txtValue}
        >
          {capitalize(
            DateTimeHelpers.formatToString({
              timezone,
              date: endDate as IDate,
              typeFormat: TypeFormatDate.DateFullWithDay,
            }),
          )}
        </CText>
      </BlockView>
      <BlockView style={styles.group}>
        <CText style={styles.txtVLabel}>{t('NUMBER_OF_SESSION')}</CText>
        <CText
          testID="session"
          style={styles.txtValue}
        >
          {t('SESSION', { t: price?.session })}
        </CText>
      </BlockView>
    </BlockView>
  );
};
