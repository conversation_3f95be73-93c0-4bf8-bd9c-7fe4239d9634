import React, { useMemo } from 'react';
import {
  Alert,
  BlockView,
  Card,
  ColorsV2,
  CText,
  FastImage,
  IconAssets,
  IconImage,
  ISO_CODE,
  Spacing,
  TouchableOpacity,
  useAppStore,
} from '@btaskee/design-system';

import { useAppNavigation, useI18n } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

export const VATInvoice = () => {
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const { isoCode } = useAppStore();
  const { vatInfo, price } = usePostTaskStore();

  const ruleVATByCountry = useMemo(() => {
    if (isoCode === ISO_CODE.TH) {
      return [
        t('DESCRIPTION_VAT_1_TH'),
        t('DESCRIPTION_VAT_2_TH'),
        t('DESCRIPTION_VAT_3_TH'),
        t('DESCRIPTION_VAT_4_TH'),
      ];
    }
    return [
      t('DESCRIPTION_VAT_1'),
      t('DESCRIPTION_VAT_2'),
      t('DESCRIPTION_VAT_3'),
      t('DESCRIPTION_VAT_4'),
    ];
  }, [isoCode, t]);

  const showDescriptionVAT = () => {
    return Alert.alert.open({
      title: t('TITLE_MODAL_DESCRIPTION_VAT'),
      message: (
        <BlockView>
          {ruleVATByCountry?.map((text) => (
            <CText
              key={text}
              margin={{ bottom: Spacing.SPACE_04 }}
            >
              {text}
            </CText>
          ))}
        </BlockView>
      ),
      actions: [{ text: t('PT1_DETAIL_CHOOSE_MANUAL_UNDERSTOOD') }],
    });
  };

  // Nếu không có VAT thì không hiển thị
  if (!price?.vat) {
    return null;
  }

  return (
    <BlockView>
      <BlockView
        row
        style={styles.wrapPanel}
      >
        <CText
          testID="txtPanel"
          bold
          style={styles.txtPanel}
        >
          {t('OPTION_VAT_OFFICE_CLEANING')}
        </CText>
        <TouchableOpacity
          onPress={showDescriptionVAT}
          style={styles.iconDescription}
        >
          <IconImage
            source={IconAssets.icQuestion}
            color={ColorsV2.green500}
            size={16}
          />
        </TouchableOpacity>
      </BlockView>
      <Card style={styles.wrapperPayment}>
        <BlockView
          flex
          row
          jBetween
          horizontal
        >
          <BlockView
            flex
            row
            horizontal
          >
            <FastImage
              source={IconAssets.iconVAT}
              style={styles.imageIcon}
            />
            <CText flex>{t('OPTION_VAT_OFFICE_CLEANING')}</CText>
          </BlockView>
          <BlockView
            flex
            justify="flex-end"
          >
            <TouchableOpacity
              testID="btnChangeCompanyEmailAddress"
              style={styles.btnChangeCompanyEmail}
              onPress={() => navigation.navigate(RouteName.ChangeVATInfo)}
            >
              <BlockView
                row
                horizontal
              >
                <CText
                  right
                  style={styles.txtGrey}
                  numberOfLines={1}
                >
                  {vatInfo?.companyEmail || t('DEMAND_NOW')}
                </CText>
                <IconImage
                  source={IconAssets.icArrowRight}
                  color={ColorsV2.neutral800}
                  size={20}
                />
              </BlockView>
            </TouchableOpacity>
          </BlockView>
        </BlockView>
      </Card>
    </BlockView>
  );
};
