import '../i18n';

import React, { useEffect } from 'react';
import {
  BlockView,
  ColorsV2,
  ConfigHelpers,
  CText,
  FontFamily,
  getDefaultPaymentMethod,
  IconAssets,
  IconImage,
  IService,
  SERVICES,
  Spacing,
  TouchableOpacity,
  useSettingsStore,
} from '@btaskee/design-system';
import {
  createNativeStackNavigator,
  type NativeStackNavigationOptions,
} from '@react-navigation/native-stack';

import { useI18n } from '@hooks';
import {
  ChangeVATInfo,
  ChooseService,
  ConfirmAndPayment,
  NotesForTasker,
  PostTaskSuccess,
} from '@screens';
import { usePostTaskStore } from '@stores';

import { RouteName } from './RouteName';
import { MainStackParamList } from './type';

const Stack = createNativeStackNavigator<MainStackParamList>();

const MainNavigator = (route: any) => {
  const { t } = useI18n();

  const { address, setService, setAddress, setPaymentMethod } =
    usePostTaskStore();
  const { settings } = useSettingsStore();

  useEffect(() => {
    initData();
  }, []);

  const initData = async () => {
    const serviceName = SERVICES.OFFICE_CLEANING_SUBSCRIPTION;
    const service = settings?.services?.find(
      (ser: IService) => ser?.name === serviceName,
    );
    setService(service);
    setAddress(route?.address);
    setPaymentMethod(
      getDefaultPaymentMethod({
        // type: TYPE_OF_PAYMENT.subscription,
        serviceName,
      }),
    );

    if (ConfigHelpers.isE2ETesting && ConfigHelpers.requireAddress) {
      setAddress(ConfigHelpers.requireAddress);
    }
  };

  const renderTitle = () => {
    return (
      <BlockView
        flex
        row
        horizontal
      >
        <IconImage
          source={IconAssets.icLocation}
          size={24}
          color={ColorsV2.red500}
        />
        <BlockView margin={{ left: Spacing.SPACE_08 }}>
          <CText>{address?.shortAddress}</CText>
          <CText
            bold
            numberOfLines={1}
            margin={{ right: Spacing.SPACE_16 }}
          >
            {address?.address}
          </CText>
        </BlockView>
      </BlockView>
    );
  };

  const renderHeaderLeft = (navigation: any) => {
    return (
      <TouchableOpacity
        onPress={() => navigation?.goBack()}
        activeOpacity={0.7}
      >
        <IconImage
          source={IconAssets.icBack}
          size={24}
          color={ColorsV2.neutral800}
        />
      </TouchableOpacity>
    );
  };

  return (
    <Stack.Navigator
      screenOptions={({ navigation }): NativeStackNavigationOptions => ({
        headerShown: true,
        headerLeft: () => renderHeaderLeft(navigation),
        animation: 'slide_from_right',
        animationDuration: 200,
        contentStyle: { backgroundColor: ColorsV2.neutralWhite },
        headerStyle: {
          backgroundColor: ColorsV2.neutralWhite,
        },
        headerTitleStyle: {
          color: ColorsV2.neutral800,
          fontSize: 18,
          fontFamily: FontFamily.bold,
        },
      })}
      initialRouteName={RouteName.ChooseService}
    >
      <Stack.Screen
        name={RouteName.ChooseService}
        component={ChooseService}
        options={{ headerTitle: renderTitle }}
      />
      <Stack.Screen
        name={RouteName.ConfirmAndPayment}
        component={ConfirmAndPayment}
        options={{ title: t('PT2_CONFIRM_HEADER_TITLE') }}
      />
      <Stack.Screen
        name={RouteName.NotesForTasker}
        component={NotesForTasker}
        options={{ title: t('LABEL_NOTE_FOR_TASKER') }}
      />
      <Stack.Screen
        name={RouteName.ChangeVATInfo}
        component={ChangeVATInfo}
        options={{ title: t('CHANGE_VAT_INFO') }}
      />
      <Stack.Screen
        name={RouteName.PostTaskSuccess}
        component={PostTaskSuccess}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;
