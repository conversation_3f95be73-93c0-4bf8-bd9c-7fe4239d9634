import React, { useEffect, useMemo, useState } from 'react';
import {
  BlockView,
  CheckBox,
  ConditionView,
  CText,
  CTextInput,
  DeviceHelper,
  IconAssets,
  IconImage,
  ISO_CODE,
  IVatInfo,
  Maybe,
  PrimaryButton,
  ScrollView,
  SERVICES,
  useAppStore,
  useUserStore,
  validEmail,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useAppNavigation, useI18n } from '@hooks';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

const height = DeviceHelper.WINDOW.HEIGHT;

export const ChangeVATInfo = () => {
  const navigation = useAppNavigation();

  const { t } = useI18n();
  const { isoCode } = useAppStore();
  const { user } = useUserStore();
  const {
    service,
    vatInfo,
    setVatInfo,
    isUpdateVatInfoToUser,
    setIsUpdateVatInfoToUser,
  } = usePostTaskStore();

  const taxCodeRef = React.createRef();
  const addressRef = React.createRef();
  const companyNameRef = React.createRef();
  const companyAddressRef = React.createRef();
  const [objAddress, setObjAddress] = useState<Maybe<IVatInfo>>(null);

  useEffect(() => {
    navigation.setOptions({ title: t('ADD_COMPANY_EMAIL_TITLE') });
    // Nếu đã nhập thì lấy lại thông tin đã nhập
    if (!isEmpty(vatInfo)) {
      return setObjAddress(vatInfo);
    }
    // Kiểm tra nếu đã có thông tin VAT thì lấy thông tin VAT
    if (!isEmpty((user as any)?.vatInfo)) {
      return setObjAddress((user as any)?.vatInfo);
    }
    // Kiểm tra nếu không có thông tin VAT thì lấy email của user
    if (!isEmpty((user as any)?.emails) && !isEmpty((user as any)?.emails[0])) {
      return setObjAddress((prevState) => ({
        ...(prevState ?? {}),
        companyEmail: (user as any)?.emails[0].address,
      }));
    }
  }, []);

  // Check disabled button by type
  // Check element in object null
  const _checkDisabledType = useMemo(() => {
    if (isEmpty(objAddress)) {
      return true;
    }
    const { companyAddress, companyEmail, companyName, taxCode } = objAddress!;
    if (
      !companyAddress ||
      !companyEmail ||
      !companyName ||
      !taxCode ||
      !validEmail(companyEmail)
    ) {
      return true;
    }
    return false;
  }, [objAddress]);

  const _renderInputCompanyAddress = useMemo(() => {
    return (
      <CTextInput
        testID="txtCompanyAddress"
        ref={companyAddressRef}
        validType={'required'}
        value={objAddress?.companyAddress || ''}
        style={styles.textInputStyle}
        labelStyle={styles.labelStyle}
        inputStyle={styles.inputStyle}
        inputContainerStyle={styles.inputContainerStyle}
        label={t('ADDRESS')}
        maxLength={200}
        placeholder={t('CREATE_LOCATION_PLACE_HOLDER_ADDRESS')}
        onChangeText={(value) =>
          setObjAddress((prevState) => ({
            ...prevState,
            companyAddress: value,
          }))
        }
      />
    );
  }, [objAddress?.companyAddress, t]);

  const _renderInputAddress = useMemo(() => {
    return (
      <CTextInput
        testID="txtEmail"
        ref={addressRef}
        validType={'required email'}
        label={t('EMAIL')}
        maxLength={100}
        style={styles.textInputStyle}
        labelStyle={styles.labelStyle}
        defaultValue={objAddress?.companyEmail || ''}
        inputContainerStyle={styles.inputContainerStyle}
        placeholder={t('PLACEHOLDER_EMAIL')}
        onChangeText={(value) =>
          setObjAddress((prevState) => ({
            ...prevState,
            companyEmail: value,
          }))
        }
      />
    );
  }, [objAddress?.companyEmail, t]);

  const _renderInputCompanyName = useMemo(() => {
    return (
      <CTextInput
        testID={'txtNameOfCompany'}
        ref={companyNameRef}
        validType={'required'}
        style={styles.textInputStyle}
        labelStyle={styles.labelStyle}
        inputStyle={styles.inputStyle}
        value={objAddress?.companyName || ''}
        maxLength={200}
        label={t('NAME_OF_COMPANY')}
        inputContainerStyle={styles.inputContainerStyle}
        leftIcon={
          <IconImage
            source={IconAssets.icCompany}
            style={styles.iconLeftInput}
          />
        }
        onChangeText={(value) =>
          setObjAddress((prevState) => ({
            ...prevState,
            companyName: value,
          }))
        }
        placeholder={t('NAME_OF_COMPANY_PLACE_HOLDER')}
      />
    );
  }, [objAddress?.companyName, t]);

  const _renderInputTaxCode = useMemo(() => {
    return (
      <CTextInput
        ref={taxCodeRef}
        testID="txtTaxCode"
        // keyboardType={'phone-pad'}
        validType={'required'}
        style={styles.textInputStyle}
        labelStyle={styles.labelStyle}
        inputStyle={styles.inputStyle}
        value={objAddress?.taxCode || ''}
        label={t('TAX_CODE_LABEL')}
        maxLength={30}
        inputContainerStyle={styles.inputContainerStyle}
        leftIcon={
          <IconImage
            source={IconAssets.icTaxCode}
            style={styles.iconLeftInput}
          />
        }
        onChangeText={(value) =>
          setObjAddress((prevState) => ({
            ...prevState,
            taxCode: value,
          }))
        }
        placeholder={t('TAX_CODE_PLACE_HOLDER')}
      />
    );
  }, [objAddress?.taxCode, t]);

  const _renderSetIsDefault = useMemo(() => {
    if (service?.name === SERVICES.OFFICE_CARPET_CLEANING) {
      return null;
    }
    return (
      <BlockView
        row
        jBetween
        style={styles.wrapSetDefault}
      >
        <CheckBox
          checked={isUpdateVatInfoToUser}
          onChecked={() => setIsUpdateVatInfoToUser(!isUpdateVatInfoToUser)}
          title={t('SAVE_INFO')}
        />
      </BlockView>
    );
  }, [isUpdateVatInfoToUser, service?.name, setIsUpdateVatInfoToUser, t]);

  const renderSubmit = useMemo(() => {
    return (
      <BlockView style={styles.boxFooter}>
        <PrimaryButton
          disabled={_checkDisabledType}
          onPress={() => {
            if (objAddress) {
              setVatInfo(objAddress);
              navigation.goBack();
            }
          }}
          title={isUpdateVatInfoToUser ? t('SAVE') : t('SEND_REQUEST')}
          style={styles.buttonStyle}
        />
      </BlockView>
    );
  }, [
    objAddress,
    isUpdateVatInfoToUser,
    _checkDisabledType,
    setVatInfo,
    navigation,
    t,
  ]);

  const ruleVATByCountry = useMemo(() => {
    // Handle null/undefined service safely
    if (!service || service.name === SERVICES.OFFICE_CLEANING) {
      return {};
    }
    if (isoCode === ISO_CODE.TH) {
      return {
        description: [
          t('DESCRIPTION_VAT_1_TH'),
          t('DESCRIPTION_VAT_2_TH'),
          t('DESCRIPTION_VAT_3_TH'),
          t('DESCRIPTION_VAT_4_TH'),
        ],
        note: t('NOTE_TAX_CODE_TH'),
      };
    }
    return {
      description: [
        t('DESCRIPTION_VAT_1'),
        t('DESCRIPTION_VAT_2'),
        t('DESCRIPTION_VAT_3'),
        t('DESCRIPTION_VAT_4'),
      ],
      note: t('NOTE_TAX_CODE'),
    };
  }, [isoCode, service, t]);

  const _warningDescription = useMemo(() => {
    return (
      <BlockView
        row
        style={styles.wrapWarning}
      >
        <IconImage
          source={IconAssets.icWarning}
          style={styles.iconLeftInput}
        />
        <BlockView
          flex
          style={styles.wrapTxtWarning}
        >
          <CText
            testID={'txtWarningEmail'}
            style={styles.txtWarning}
          >
            {t('WARNING_ADD_EMAIL')}
          </CText>
        </BlockView>
      </BlockView>
    );
  }, [t]);

  return (
    <BlockView
      inset={'bottom'}
      style={styles.container}
    >
      <BlockView style={styles.content}>
        <ScrollView
          testID="scrollAddLocation2"
          style={{ marginBottom: height * 0.1 }}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
        >
          {_warningDescription}
          {_renderInputAddress}
          {_renderInputCompanyName}
          {_renderInputTaxCode}
          <ConditionView
            condition={Boolean(ruleVATByCountry.note)}
            viewTrue={
              <BlockView style={styles.wrapNoteTaxCode}>
                <CText style={styles.txtNoteTaxCode}>
                  {ruleVATByCountry.note}
                </CText>
              </BlockView>
            }
          />
          {_renderInputCompanyAddress}
          {_renderSetIsDefault}
          <ConditionView
            condition={Boolean(ruleVATByCountry.description)}
            viewTrue={
              <BlockView style={styles.wrapDescription}>
                <CText
                  bold
                  style={styles.txtDescription}
                >
                  {t('TITLE_MODAL_DESCRIPTION_VAT')}
                </CText>
                {ruleVATByCountry.description?.map((item) => (
                  <CText
                    key={item}
                    style={styles.txtDescription}
                  >
                    {item}
                  </CText>
                ))}
              </BlockView>
            }
          />
        </ScrollView>
        {renderSubmit}
      </BlockView>
    </BlockView>
  );
};
