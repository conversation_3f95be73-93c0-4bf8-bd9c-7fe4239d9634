import React, { useMemo } from 'react';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import {
  Alert,
  BlockView,
  ChangeScheduleSub,
  ChooseMonth,
  ColorsV2,
  ConditionView,
  CText,
  DateTimeHelpers,
  FontFamily,
  FontSizes,
  formatMoney,
  getDiscountMonthByCity,
  IconAssets,
  IconImage,
  IDate,
  IPriceSub,
  IService,
  PostTaskHelpers,
  PriceButtonSubscription,
  RepeatWeekly,
  ScrollView,
  Spacing,
  TimePicker,
  TouchableOpacity,
  useSettingsStore,
} from '@btaskee/design-system';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { isEmpty } from 'lodash-es';

import { AddOnServices, ChooseDuration } from '@components';
import { useAppNavigation, useChangeData, useI18n, usePostTask } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { ParamsNavigationList } from '@navigation/type';
import { usePostTaskStore } from '@stores';

import styles from './styles';

type ChooseServiceRoute = NativeStackScreenProps<
  ParamsNavigationList,
  RouteName.ChooseService
>;

export const ChooseService = (route: ChooseServiceRoute) => {
  const navigation = useAppNavigation();

  const { t } = useI18n();
  const { settings } = useSettingsStore();

  const {
    address,
    duration,
    month,
    service,
    price,
    weekdays,
    startDate,
    isLoadingPrice,
    setStartDate,
    resetState,
    schedule,
    setSchedule,
  } = usePostTaskStore();

  const {
    onChangeMonth,
    onChangeDateTime,
    onChangeRepeatWeekly,
    setDataPTSubscription,
  } = useChangeData();

  const { getPrice } = usePostTask();

  // Get data from notification
  const renewOldSubscription = route?.params?.renewOldSubscription; // Data from renew subscription

  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

  const optionToolBar = useMemo(() => {
    return {
      headerTitle: (
        <BlockView
          flex
          row
          horizontal
        >
          <IconImage
            source={IconAssets.icLocation}
            size={24}
            color={ColorsV2.red500}
          />
          <BlockView margin={{ left: Spacing.SPACE_08 }}>
            <CText>{address?.shortAddress}</CText>
            <CText
              bold
              numberOfLines={1}
              margin={{ right: Spacing.SPACE_16 }}
            >
              {address?.address}
            </CText>
          </BlockView>
        </BlockView>
      ),
    };
  }, [address]);

  React.useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: () => optionToolBar.headerTitle,
    });
  }, [navigation, optionToolBar.headerTitle]);

  React.useEffect(() => {
    const days = settings?.numberOfDaysPostTaskFromNow || 3; //get days min post task
    const hours = settings?.defaultTaskTime || 8; // get time post task
    const defaultDate = DateTimeHelpers.formatToString({
      timezone,
      date: DateTimeHelpers.toDayTz({ timezone })
        .add(days, 'day')
        .set('hour', hours)
        .startOf('hour'),
    });
    setStartDate?.(defaultDate);

    // navigate from renew subscription in task subscription
    if (!isEmpty(renewOldSubscription)) {
      setDataPTSubscription?.(renewOldSubscription);
    }

    return () => {
      resetState?.();
    };
  }, []);

  const onConfirmed = () => {
    const postingLimits = service?.postingLimits;
    // check posting limit, ex 6AM - 10PM
    if (
      !PostTaskHelpers.checkTimeValidFromService(
        timezone,
        startDate as IDate,
        duration,
        postingLimits,
      )
    ) {
      const postingLimitsFormat = PostTaskHelpers.formatPostingLimits({
        timezone,
        postingLimits,
      });

      return Alert.alert.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('NEW_POST_TASK_STEP2_TIME_INVALID', {
          from: postingLimitsFormat.from,
          to: postingLimitsFormat.to,
        }),
        actions: [{ text: t('CLOSE') }],
      });
    }

    // data ok
    onNextStep();
  };

  const onNextStep = async () => {
    // setStepPostTask?.(TRACKING_STEP.STEP_4);
    navigation.navigate(RouteName.NotesForTasker);
  };

  const priceDiscount = useMemo(() => {
    const discount = price?.cost - price?.finalCost;
    if (discount > 0) {
      return (
        <CText style={styles.discountTxt}>
          {t('CHILD_CARE_DISCOUNT_PRICE', {
            cost: formatMoney(discount),
            currency: settings?.currency?.sign,
          })}
        </CText>
      );
    }
    return null;
  }, [price, settings?.currency?.sign, t]);

  const openSchedule = () => {
    Alert.alert.open({
      title: t('WORKING_SCHEDULE'),
      message: (
        <ChangeScheduleSub
          schedule={schedule}
          weekDays={weekdays}
          getPriceSubscription={getPrice}
          updateScheduleSubscription={setSchedule}
          pricing={price?.pricing}
          service={service as IService}
          setStartDate={setStartDate}
          timezone={timezone}
        />
      ),
      contentContainerStyle: {
        paddingHorizontal: 0,
      },
      titleStyle: {
        fontSize: FontSizes.SIZE_20,
        fontWeight: 'bold',
        fontFamily: FontFamily.bold,
      },
    });
  };

  const discountByMonth = getDiscountMonthByCity(address?.city);

  return (
    <BlockView style={{ backgroundColor: ColorsV2.neutralBackground }}>
      <ScrollView
        testID="scrollStep2Cleaning"
        contentContainerStyle={styles.containerScroll}
        showsVerticalScrollIndicator={false}
      >
        <RepeatWeekly
          isHideWeekly
          isEnabled={true}
          style={styles.weekly}
          weeklyRepeater={weekdays}
          onChange={onChangeRepeatWeekly}
          contentStyle={styles.contentWeeklyStyle}
          titleStyle={styles.txtTitleRepeatWeekly}
          title={t('SELECT_WORK_SCHEDULE')}
        />

        <ConditionView
          condition={!isEmpty(schedule)}
          viewTrue={
            <BlockView margin={{ bottom: Spacing.SPACE_16 }}>
              <TouchableOpacity onPress={openSchedule}>
                <Animated.View
                  entering={FadeIn}
                  exiting={FadeOut}
                >
                  <BlockView
                    horizontal
                    jBetween
                    row
                    style={styles.boxContent}
                  >
                    <CText style={styles.txtCheck}>
                      {t('CHECK_SCHEDULE_SUBSCRIPTION')}
                    </CText>
                    <IconImage source={IconAssets.icCalendar} />
                  </BlockView>
                </Animated.View>
              </TouchableOpacity>
            </BlockView>
          }
        />
        <BlockView>
          <CText style={styles.txtPanel}>{t('CHOOSE_TIME_START')}</CText>
          <TimePicker
            isHideLabel={true}
            value={startDate as IDate}
            onChange={onChangeDateTime}
            settingSystem={settings?.settingSystem}
            style={styles.contentTimePicker}
            timeStyle={styles.timeContainerStyle}
            containerStyle={styles.wrapperTimePicker}
            timezone={timezone}
          />
        </BlockView>

        <ChooseMonth
          month={month}
          onChange={onChangeMonth}
          service={service as IService}
          discountByMonth={discountByMonth}
        />

        <ChooseDuration />
        {/* <SizedBox height={Spacing.SPACE_20} /> */}
        <AddOnServices />
        {priceDiscount}
      </ScrollView>
      <PriceButtonSubscription
        testID="btnNextStep2"
        onPress={onConfirmed}
        price={price as IPriceSub}
        isLoading={isLoadingPrice}
      />
    </BlockView>
  );
};
