import React from 'react';
import {
  BlockView,
  CText,
  DeviceHelper,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import LottieView from 'lottie-react-native';

import { useAppNavigation, useI18n } from '@hooks';

import styles from './styles';

export const PostTaskSuccess = () => {
  const { t } = useI18n();
  const navigation = useAppNavigation();

  const _followTask = () => {
    // back to home page
    navigation.popToTop();
    navigation.navigate('TabNavigator', { screen: 'Tab_Activity' });
  };

  const _goHome = () => {
    navigation.popToTop();
  };

  return (
    <BlockView
      inset={'bottom'}
      style={styles.container}
    >
      {/* Content */}
      <BlockView
        flex
        center
      >
        <LottieView
          source={require('../../assets/lottie/tick.json')}
          style={{
            width: DeviceHelper.WINDOW.WIDTH / 2,
            height: DeviceHelper.WINDOW.WIDTH / 2,
          }}
          resizeMode="contain"
          autoPlay={true}
        />
        <BlockView
          style={{
            paddingHorizontal: Spacing.SPACE_24,
            marginTop: Spacing.SPACE_16,
          }}
        >
          <CText
            bold
            style={styles.modalTitleStyle}
          >
            {t('MODAL_POST_TASK_SUCCESS_TITLE')}
          </CText>
          <CText style={styles.txtContent}>
            {t('MODAL_POST_TASK_SUCCESS_CONTENT')}
          </CText>
        </BlockView>
      </BlockView>

      {/* Action */}
      <BlockView
        inset={'bottom'}
        style={styles.btnActionBox}
      >
        <TouchableOpacity
          testID="postTaskSuccessBtn"
          onPress={_followTask}
          style={styles.touchableFollowTask}
        >
          <CText
            bold
            style={styles.txtBtnFollowTask}
          >
            {t('MODAL_POST_TASK_SUCCESS_BTN_FOLLOW_TASK')}
          </CText>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={_goHome}
          style={styles.touchableGoHome}
        >
          <CText
            bold
            style={styles.txtBtnGoHome}
          >
            {t('MODAL_POST_TASK_SUCCESS_BTN_GO_HOME')}
          </CText>
        </TouchableOpacity>
      </BlockView>
    </BlockView>
  );
};
