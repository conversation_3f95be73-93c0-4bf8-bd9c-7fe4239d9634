import '../i18n';

import React, { useEffect } from 'react';
import {
  BlockView,
  Colors,
  ConfigHelpers,
  CText,
  FontFamily,
  FontSizes,
  getDefaultPaymentMethod,
  IconAssets,
  IconImage,
  IService,
  SERVICES,
  Spacing,
  TouchableOpacity,
  useSettingsStore,
} from '@btaskee/design-system';
import {
  createNativeStackNavigator,
  type NativeStackNavigationOptions,
} from '@react-navigation/native-stack';

import { useI18n } from '@hooks';
import {
  ChangeVATInfo,
  ChooseAddress,
  ChooseDateTime,
  ChooseService,
  ChooseWorkTime,
  ConfirmAndPayment,
  IntroService,
  PostTaskSuccess,
  WorkingProcess,
} from '@screens';
import { usePostTaskStore } from '@stores';

import { RouteName } from './RouteName';
import { MainStackParamList } from './type';

const Stack = createNativeStackNavigator<MainStackParamList>();

const MainNavigator = () => {
  const { t } = useI18n();

  const { address, setService, isFirstOpen, setPaymentMethod } =
    usePostTaskStore();
  const { settings } = useSettingsStore();

  useEffect(() => {
    initData();
  }, []);

  // TODO: init data for cleaning service
  const initData = async () => {
    const officeCleaningService = settings?.services?.find(
      (service: IService) => service?.name === SERVICES.OFFICE_CLEANING,
    );
    setService(officeCleaningService);
    setPaymentMethod(
      getDefaultPaymentMethod({ serviceName: SERVICES.OFFICE_CLEANING }),
    );
  };

  const renderTitle = () => {
    return (
      <BlockView
        flex
        row
        horizontal
      >
        <IconImage
          source={IconAssets.icLocation}
          size={24}
          color={Colors.RED}
        />
        <BlockView margin={{ left: Spacing.SPACE_08 }}>
          <CText>{address?.shortAddress}</CText>
          <CText
            bold
            numberOfLines={1}
            margin={{ right: Spacing.SPACE_16 }}
          >
            {address?.address}
          </CText>
        </BlockView>
      </BlockView>
    );
  };

  const renderHeaderLeft = ({
    navigation,
    colorIcon,
  }: {
    navigation: any;
    colorIcon?: Colors;
  }) => {
    return (
      <TouchableOpacity
        onPress={() => navigation?.goBack()}
        activeOpacity={0.7}
      >
        <IconImage
          source={IconAssets.icBack}
          size={24}
          color={colorIcon || Colors.BLACK}
        />
      </TouchableOpacity>
    );
  };

  return (
    <Stack.Navigator
      screenOptions={({ navigation }): NativeStackNavigationOptions => ({
        headerShown: true,
        headerLeft: () => renderHeaderLeft({ navigation }),
        animation: 'slide_from_right',
        animationDuration: 200,
        contentStyle: { backgroundColor: Colors.WHITE },
        headerStyle: {
          backgroundColor: Colors.WHITE,
        },
        headerTitleStyle: {
          color: Colors.BLACK,
          fontSize: FontSizes.SIZE_18,
          fontFamily: FontFamily.bold,
        },
      })}
      initialRouteName={
        isFirstOpen && !ConfigHelpers.isE2ETesting
          ? RouteName.IntroService
          : RouteName.ChooseAddress
      }
    >
      <Stack.Screen
        name={RouteName.IntroService}
        component={IntroService}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={RouteName.ChooseAddress}
        component={ChooseAddress}
        options={{ title: t('LIST_OF_LOCATIONS') }}
      />
      <Stack.Screen
        name={RouteName.ChooseService}
        component={ChooseService}
        options={{ headerTitle: renderTitle }}
      />
      <Stack.Screen
        name={RouteName.ChooseWorkTime}
        component={ChooseWorkTime}
        options={({ navigation }) => {
          return {
            title: t('INTRO_OFFICE_CLEANING_NAME'),
            headerTransparent: true,
            headerTintColor: Colors.WHITE,
            headerTitleStyle: {
              color: Colors.WHITE,
            },
            headerStyle: {
              backgroundColor: 'transparent',
            },
            headerLeft: () =>
              renderHeaderLeft({ navigation, colorIcon: Colors.WHITE }),
          };
        }}
      />
      <Stack.Screen
        name={RouteName.WorkingProcessDetail}
        component={WorkingProcess}
        options={{ title: t('TASK_DETAIL') }}
      />
      <Stack.Screen
        name={RouteName.ChooseDateTime}
        component={ChooseDateTime}
        options={{ title: t('WORK_TIME_TITLE') }}
      />
      <Stack.Screen
        name={RouteName.ConfirmAndPayment}
        component={ConfirmAndPayment}
        options={{ title: t('PT2_CONFIRM_HEADER_TITLE') }}
      />
      <Stack.Screen
        name={RouteName.PostTaskSuccess}
        component={PostTaskSuccess}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={RouteName.ChangeVATInfo}
        component={ChangeVATInfo}
        options={{ title: t('CHANGE_VAT_INFO') }}
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;
